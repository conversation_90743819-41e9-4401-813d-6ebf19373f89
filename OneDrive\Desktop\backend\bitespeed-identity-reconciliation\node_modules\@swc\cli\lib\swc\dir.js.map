{"version": 3, "sources": ["../../src/swc/dir.ts"], "sourcesContent": ["import { existsSync, promises } from \"fs\";\nimport { dirname, resolve } from \"path\";\nimport <PERSON>s<PERSON><PERSON> from \"piscina\";\nimport { stderr } from \"process\";\nimport { format } from \"util\";\nimport { CompileStatus } from \"./constants\";\nimport { Callbacks, CliOptions } from \"./options\";\nimport { exists, getDest } from \"./util\";\nimport handleCompile from \"./dirWorker\";\nimport {\n    globSources,\n    isCompilableExtension,\n    splitCompilableAndCopyable,\n    watchSources,\n} from \"./sources\";\n\nimport type { Options } from \"@swc/core\";\n\ndeclare module \"fs\" {\n    namespace promises {\n        /**\n         * For node > 14 we want to use rm instead of rmdir\n         * We need to augment node v12 types\n         */\n        function rm(dir: string, option: object): void;\n    }\n}\n\nconst { mkdir, rmdir, rm, copyFile, unlink } = promises;\n\nconst recursive = { recursive: true };\n\nasync function handleCopy(\n    filename: string,\n    outDir: string,\n    stripLeadingPaths: boolean\n) {\n    const dest = getDest(filename, outDir, stripLeadingPaths);\n    const dir = dirname(dest);\n\n    await mkdir(dir, recursive);\n    await copyFile(filename, dest);\n\n    return CompileStatus.Copied;\n}\n\nasync function beforeStartCompilation(cliOptions: CliOptions) {\n    const { outDir, deleteDirOnStart } = cliOptions;\n\n    if (deleteDirOnStart) {\n        const exists = await existsSync(outDir);\n        if (exists) {\n            rm ? await rm(outDir, recursive) : await rmdir(outDir, recursive);\n        }\n    }\n}\n\nasync function initialCompilation(\n    cliOptions: CliOptions,\n    swcOptions: Options,\n    callbacks?: Callbacks\n) {\n    const {\n        includeDotfiles,\n        filenames,\n        copyFiles,\n        extensions,\n        outDir,\n        outFileExtension,\n        stripLeadingPaths,\n        sync,\n        quiet,\n        watch,\n        only,\n        ignore,\n    } = cliOptions;\n\n    const results = new Map<string, CompileStatus>();\n    const reasons = new Map<string, string>();\n\n    const start = process.hrtime();\n    const sourceFiles = await globSources(\n        filenames,\n        only,\n        ignore,\n        includeDotfiles\n    );\n    const [compilable, copyable] = splitCompilableAndCopyable(\n        sourceFiles,\n        extensions,\n        copyFiles\n    );\n    if (sync) {\n        for (const filename of compilable) {\n            try {\n                const result = await handleCompile({\n                    filename,\n                    outDir,\n                    sync,\n                    cliOptions,\n                    swcOptions,\n                    outFileExtension,\n                });\n                results.set(filename, result);\n            } catch (err: any) {\n                if (!callbacks?.onFail) {\n                    console.error(err.message);\n                }\n                results.set(filename, CompileStatus.Failed);\n            }\n        }\n        for (const filename of copyable) {\n            try {\n                const result = await handleCopy(\n                    filename,\n                    outDir,\n                    stripLeadingPaths\n                );\n                results.set(filename, result);\n            } catch (err: any) {\n                if (!callbacks?.onFail) {\n                    console.error(err.message);\n                }\n                results.set(filename, CompileStatus.Failed);\n            }\n        }\n    } else {\n        const workers = new Piscina({\n            filename: resolve(__dirname, \"./dirWorker.js\"),\n            maxThreads: cliOptions.workers,\n            concurrentTasksPerWorker: 2,\n        });\n\n        await Promise.all([\n            Promise.allSettled(\n                compilable.map(filename =>\n                    workers\n                        .run({\n                            filename,\n                            outDir,\n                            sync,\n                            cliOptions,\n                            swcOptions,\n                            outFileExtension,\n                        })\n                        .catch(err => {\n                            if (!callbacks?.onFail) {\n                                console.error(err.message);\n                            }\n                            throw err;\n                        })\n                )\n            ),\n            Promise.allSettled(\n                copyable.map(file =>\n                    handleCopy(file, outDir, stripLeadingPaths)\n                )\n            ),\n        ]).then(([compiled, copied]) => {\n            compiled.forEach((result, index) => {\n                const filename = compilable[index];\n                if (result.status === \"fulfilled\") {\n                    results.set(filename, result.value);\n                } else {\n                    results.set(filename, CompileStatus.Failed);\n                    reasons.set(filename, result.reason.message);\n                }\n            });\n\n            copied.forEach((result, index) => {\n                const filename = copyable[index];\n                if (result.status === \"fulfilled\") {\n                    results.set(filename, result.value);\n                } else {\n                    results.set(filename, CompileStatus.Failed);\n                }\n            });\n        });\n    }\n    const end = process.hrtime(start);\n\n    let failed = 0;\n    let compiled = 0;\n    let copied = 0;\n    for (let [_, status] of results) {\n        switch (status) {\n            case CompileStatus.Compiled:\n                compiled += 1;\n                break;\n            case CompileStatus.Failed:\n                failed += 1;\n                break;\n            case CompileStatus.Copied:\n                copied += 1;\n                break;\n        }\n    }\n    const duration = end[1] / 1000000;\n\n    if (compiled + copied) {\n        let message = \"\";\n        if (compiled) {\n            message += `Successfully compiled: ${compiled} ${\n                compiled > 1 ? \"files\" : \"file\"\n            }`;\n        }\n        if (compiled && copied) {\n            message += \", \";\n        }\n        if (copied) {\n            message += `copied ${copied} ${copied > 1 ? \"files\" : \"file\"}`;\n        }\n        message += format(\" with swc (%dms)\\n\", duration.toFixed(2));\n\n        if (callbacks?.onSuccess) {\n            if (!failed) {\n                callbacks.onSuccess({ duration, compiled, copied });\n            }\n        } else if (!quiet) {\n            stderr.write(message);\n        }\n    }\n\n    if (failed) {\n        if (callbacks?.onFail) {\n            callbacks.onFail({ duration, reasons });\n        } else {\n            console.error(\n                `Failed to compile ${failed} ${\n                    failed !== 1 ? \"files\" : \"file\"\n                } with swc.`\n            );\n            if (!watch) {\n                const files = Array.from(results.entries())\n                    .filter(([, status]) => status === CompileStatus.Failed)\n                    .map(([filename, _]) => filename)\n                    .join(\"\\n\");\n                throw new Error(`Failed to compile:\\n${files}`);\n            }\n        }\n    }\n}\n\nasync function watchCompilation(\n    cliOptions: CliOptions,\n    swcOptions: Options,\n    callbacks?: Callbacks\n) {\n    const {\n        includeDotfiles,\n        filenames,\n        copyFiles,\n        extensions,\n        outDir,\n        stripLeadingPaths,\n        outFileExtension,\n        quiet,\n        sync,\n    } = cliOptions;\n\n    const watcher = await watchSources(filenames, includeDotfiles);\n    watcher.on(\"ready\", () => {\n        if (callbacks?.onWatchReady) {\n            callbacks.onWatchReady();\n        } else if (!quiet) {\n            console.info(\"Watching for file changes.\");\n        }\n    });\n    watcher.on(\"unlink\", async filename => {\n        try {\n            if (isCompilableExtension(filename, extensions)) {\n                await unlink(\n                    getDest(filename, outDir, stripLeadingPaths, \".js\")\n                );\n                const sourcemapPath = getDest(\n                    filename,\n                    outDir,\n                    stripLeadingPaths,\n                    \".js.map\"\n                );\n                const sourcemapExists = await exists(sourcemapPath);\n                if (sourcemapExists) {\n                    await unlink(sourcemapPath);\n                }\n            } else if (copyFiles) {\n                await unlink(getDest(filename, outDir, stripLeadingPaths));\n            }\n        } catch (err: any) {\n            if (err.code !== \"ENOENT\") {\n                console.error(err.stack);\n            }\n        }\n    });\n    for (const type of [\"add\", \"change\"]) {\n        watcher.on(type, async filename => {\n            if (isCompilableExtension(filename, extensions)) {\n                const start = process.hrtime();\n                const getDuration = () => {\n                    const end = process.hrtime(start);\n                    const duration = end[1] / 1000000;\n                    return duration;\n                };\n                try {\n                    const result = await handleCompile({\n                        filename,\n                        outDir,\n                        sync,\n                        cliOptions,\n                        swcOptions,\n                        outFileExtension,\n                    });\n                    const duration = getDuration();\n                    if (result === CompileStatus.Compiled) {\n                        if (callbacks?.onSuccess) {\n                            callbacks.onSuccess({\n                                duration,\n                                compiled: 1,\n                                filename,\n                            });\n                        } else if (!quiet) {\n                            stderr.write(\n                                format(\n                                    `Successfully compiled ${filename} with swc (%dms)\\n`,\n                                    duration.toFixed(2)\n                                )\n                            );\n                        }\n                    }\n                } catch (error: any) {\n                    if (callbacks?.onFail) {\n                        const reasons = new Map<string, string>();\n                        reasons.set(filename, error.message);\n                        callbacks.onFail({ duration: getDuration(), reasons });\n                    } else {\n                        console.error(error.message);\n                    }\n                }\n            } else if (copyFiles) {\n                const start = process.hrtime();\n                const getDuration = () => {\n                    const end = process.hrtime(start);\n                    const duration = end[1] / 1000000;\n                    return duration;\n                };\n                try {\n                    const result = await handleCopy(\n                        filename,\n                        outDir,\n                        stripLeadingPaths\n                    );\n                    if (result === CompileStatus.Copied) {\n                        const duration = getDuration();\n                        if (callbacks?.onSuccess) {\n                            callbacks.onSuccess({\n                                duration,\n                                copied: 1,\n                                filename,\n                            });\n                        } else if (!quiet) {\n                            stderr.write(\n                                format(\n                                    `Successfully copied ${filename} with swc (%dms)\\n`,\n                                    duration.toFixed(2)\n                                )\n                            );\n                        }\n                    }\n                } catch (error: any) {\n                    if (callbacks?.onFail) {\n                        const reasons = new Map<string, string>();\n                        reasons.set(filename, error.message);\n                        callbacks.onFail({ duration: getDuration(), reasons });\n                    } else {\n                        console.error(`Failed to copy ${filename}`);\n                        console.error(error.message);\n                    }\n                }\n            }\n        });\n    }\n}\n\nexport default async function dir({\n    cliOptions,\n    swcOptions,\n    callbacks,\n}: {\n    cliOptions: CliOptions;\n    swcOptions: Options;\n    callbacks?: Callbacks;\n}) {\n    const { watch } = cliOptions;\n\n    await beforeStartCompilation(cliOptions);\n    await initialCompilation(cliOptions, swcOptions, callbacks);\n\n    if (watch) {\n        await watchCompilation(cliOptions, swcOptions, callbacks);\n    }\n}\n"], "names": ["dir", "mkdir", "rmdir", "rm", "copyFile", "unlink", "promises", "recursive", "handleCopy", "filename", "outDir", "stripLeadingPaths", "dest", "getDest", "dirname", "CompileStatus", "<PERSON>pied", "beforeStartCompilation", "cliOptions", "deleteDirOnStart", "exists", "existsSync", "initialCompilation", "swcOptions", "callbacks", "includeDotfiles", "filenames", "copyFiles", "extensions", "outFileExtension", "sync", "quiet", "watch", "only", "ignore", "results", "Map", "reasons", "start", "process", "hrtime", "sourceFiles", "globSources", "compilable", "copyable", "splitCompilableAndCopyable", "result", "handleCompile", "set", "err", "onFail", "console", "error", "message", "Failed", "workers", "Piscina", "resolve", "__dirname", "maxThreads", "concurrentTasksPerWorker", "Promise", "all", "allSettled", "map", "run", "catch", "file", "then", "compiled", "copied", "for<PERSON>ach", "index", "status", "value", "reason", "end", "failed", "_", "Compiled", "duration", "format", "toFixed", "onSuccess", "stderr", "write", "files", "Array", "from", "entries", "filter", "join", "Error", "watchCompilation", "watcher", "watchSources", "on", "onWatchReady", "info", "isCompilableExtension", "sourcemapPath", "sourcemapExists", "code", "stack", "type", "getDuration"], "mappings": ";;;;+BA8XA;;;eAA8BA;;;oBA9XO;sBACJ;gEACb;yBACG;sBACA;2BACO;uBAEE;kEACN;yBAMnB;;;;;;AAcP,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,EAAE,EAAEC,QAAQ,EAAEC,MAAM,EAAE,GAAGC,YAAQ;AAEvD,MAAMC,YAAY;IAAEA,WAAW;AAAK;AAEpC,eAAeC,WACXC,QAAgB,EAChBC,MAAc,EACdC,iBAA0B;IAE1B,MAAMC,OAAOC,IAAAA,cAAO,EAACJ,UAAUC,QAAQC;IACvC,MAAMX,MAAMc,IAAAA,aAAO,EAACF;IAEpB,MAAMX,MAAMD,KAAKO;IACjB,MAAMH,SAASK,UAAUG;IAEzB,OAAOG,wBAAa,CAACC,MAAM;AAC/B;AAEA,eAAeC,uBAAuBC,UAAsB;IACxD,MAAM,EAAER,MAAM,EAAES,gBAAgB,EAAE,GAAGD;IAErC,IAAIC,kBAAkB;QAClB,MAAMC,SAAS,MAAMC,IAAAA,cAAU,EAACX;QAChC,IAAIU,QAAQ;YACRjB,KAAK,MAAMA,GAAGO,QAAQH,aAAa,MAAML,MAAMQ,QAAQH;QAC3D;IACJ;AACJ;AAEA,eAAee,mBACXJ,UAAsB,EACtBK,UAAmB,EACnBC,SAAqB;IAErB,MAAM,EACFC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,UAAU,EACVlB,MAAM,EACNmB,gBAAgB,EAChBlB,iBAAiB,EACjBmB,IAAI,EACJC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,MAAM,EACT,GAAGhB;IAEJ,MAAMiB,UAAU,IAAIC;IACpB,MAAMC,UAAU,IAAID;IAEpB,MAAME,QAAQC,QAAQC,MAAM;IAC5B,MAAMC,cAAc,MAAMC,IAAAA,oBAAW,EACjChB,WACAO,MACAC,QACAT;IAEJ,MAAM,CAACkB,YAAYC,SAAS,GAAGC,IAAAA,mCAA0B,EACrDJ,aACAb,YACAD;IAEJ,IAAIG,MAAM;QACN,KAAK,MAAMrB,YAAYkC,WAAY;YAC/B,IAAI;gBACA,MAAMG,SAAS,MAAMC,IAAAA,kBAAa,EAAC;oBAC/BtC;oBACAC;oBACAoB;oBACAZ;oBACAK;oBACAM;gBACJ;gBACAM,QAAQa,GAAG,CAACvC,UAAUqC;YAC1B,EAAE,OAAOG,KAAU;gBACf,IAAI,EAACzB,sBAAAA,gCAAAA,UAAW0B,MAAM,GAAE;oBACpBC,QAAQC,KAAK,CAACH,IAAII,OAAO;gBAC7B;gBACAlB,QAAQa,GAAG,CAACvC,UAAUM,wBAAa,CAACuC,MAAM;YAC9C;QACJ;QACA,KAAK,MAAM7C,YAAYmC,SAAU;YAC7B,IAAI;gBACA,MAAME,SAAS,MAAMtC,WACjBC,UACAC,QACAC;gBAEJwB,QAAQa,GAAG,CAACvC,UAAUqC;YAC1B,EAAE,OAAOG,KAAU;gBACf,IAAI,EAACzB,sBAAAA,gCAAAA,UAAW0B,MAAM,GAAE;oBACpBC,QAAQC,KAAK,CAACH,IAAII,OAAO;gBAC7B;gBACAlB,QAAQa,GAAG,CAACvC,UAAUM,wBAAa,CAACuC,MAAM;YAC9C;QACJ;IACJ,OAAO;QACH,MAAMC,UAAU,IAAIC,gBAAO,CAAC;YACxB/C,UAAUgD,IAAAA,aAAO,EAACC,WAAW;YAC7BC,YAAYzC,WAAWqC,OAAO;YAC9BK,0BAA0B;QAC9B;QAEA,MAAMC,QAAQC,GAAG,CAAC;YACdD,QAAQE,UAAU,CACdpB,WAAWqB,GAAG,CAACvD,CAAAA,WACX8C,QACKU,GAAG,CAAC;oBACDxD;oBACAC;oBACAoB;oBACAZ;oBACAK;oBACAM;gBACJ,GACCqC,KAAK,CAACjB,CAAAA;oBACH,IAAI,EAACzB,sBAAAA,gCAAAA,UAAW0B,MAAM,GAAE;wBACpBC,QAAQC,KAAK,CAACH,IAAII,OAAO;oBAC7B;oBACA,MAAMJ;gBACV;YAGZY,QAAQE,UAAU,CACdnB,SAASoB,GAAG,CAACG,CAAAA,OACT3D,WAAW2D,MAAMzD,QAAQC;SAGpC,EAAEyD,IAAI,CAAC,CAAC,CAACC,UAAUC,OAAO;YACvBD,SAASE,OAAO,CAAC,CAACzB,QAAQ0B;gBACtB,MAAM/D,WAAWkC,UAAU,CAAC6B,MAAM;gBAClC,IAAI1B,OAAO2B,MAAM,KAAK,aAAa;oBAC/BtC,QAAQa,GAAG,CAACvC,UAAUqC,OAAO4B,KAAK;gBACtC,OAAO;oBACHvC,QAAQa,GAAG,CAACvC,UAAUM,wBAAa,CAACuC,MAAM;oBAC1CjB,QAAQW,GAAG,CAACvC,UAAUqC,OAAO6B,MAAM,CAACtB,OAAO;gBAC/C;YACJ;YAEAiB,OAAOC,OAAO,CAAC,CAACzB,QAAQ0B;gBACpB,MAAM/D,WAAWmC,QAAQ,CAAC4B,MAAM;gBAChC,IAAI1B,OAAO2B,MAAM,KAAK,aAAa;oBAC/BtC,QAAQa,GAAG,CAACvC,UAAUqC,OAAO4B,KAAK;gBACtC,OAAO;oBACHvC,QAAQa,GAAG,CAACvC,UAAUM,wBAAa,CAACuC,MAAM;gBAC9C;YACJ;QACJ;IACJ;IACA,MAAMsB,MAAMrC,QAAQC,MAAM,CAACF;IAE3B,IAAIuC,SAAS;IACb,IAAIR,WAAW;IACf,IAAIC,SAAS;IACb,KAAK,IAAI,CAACQ,GAAGL,OAAO,IAAItC,QAAS;QAC7B,OAAQsC;YACJ,KAAK1D,wBAAa,CAACgE,QAAQ;gBACvBV,YAAY;gBACZ;YACJ,KAAKtD,wBAAa,CAACuC,MAAM;gBACrBuB,UAAU;gBACV;YACJ,KAAK9D,wBAAa,CAACC,MAAM;gBACrBsD,UAAU;gBACV;QACR;IACJ;IACA,MAAMU,WAAWJ,GAAG,CAAC,EAAE,GAAG;IAE1B,IAAIP,WAAWC,QAAQ;QACnB,IAAIjB,UAAU;QACd,IAAIgB,UAAU;YACVhB,WAAW,CAAC,uBAAuB,EAAEgB,SAAS,CAAC,EAC3CA,WAAW,IAAI,UAAU,OAC5B,CAAC;QACN;QACA,IAAIA,YAAYC,QAAQ;YACpBjB,WAAW;QACf;QACA,IAAIiB,QAAQ;YACRjB,WAAW,CAAC,OAAO,EAAEiB,OAAO,CAAC,EAAEA,SAAS,IAAI,UAAU,OAAO,CAAC;QAClE;QACAjB,WAAW4B,IAAAA,YAAM,EAAC,sBAAsBD,SAASE,OAAO,CAAC;QAEzD,IAAI1D,sBAAAA,gCAAAA,UAAW2D,SAAS,EAAE;YACtB,IAAI,CAACN,QAAQ;gBACTrD,UAAU2D,SAAS,CAAC;oBAAEH;oBAAUX;oBAAUC;gBAAO;YACrD;QACJ,OAAO,IAAI,CAACvC,OAAO;YACfqD,eAAM,CAACC,KAAK,CAAChC;QACjB;IACJ;IAEA,IAAIwB,QAAQ;QACR,IAAIrD,sBAAAA,gCAAAA,UAAW0B,MAAM,EAAE;YACnB1B,UAAU0B,MAAM,CAAC;gBAAE8B;gBAAU3C;YAAQ;QACzC,OAAO;YACHc,QAAQC,KAAK,CACT,CAAC,kBAAkB,EAAEyB,OAAO,CAAC,EACzBA,WAAW,IAAI,UAAU,OAC5B,UAAU,CAAC;YAEhB,IAAI,CAAC7C,OAAO;gBACR,MAAMsD,QAAQC,MAAMC,IAAI,CAACrD,QAAQsD,OAAO,IACnCC,MAAM,CAAC,CAAC,GAAGjB,OAAO,GAAKA,WAAW1D,wBAAa,CAACuC,MAAM,EACtDU,GAAG,CAAC,CAAC,CAACvD,UAAUqE,EAAE,GAAKrE,UACvBkF,IAAI,CAAC;gBACV,MAAM,IAAIC,MAAM,CAAC,oBAAoB,EAAEN,MAAM,CAAC;YAClD;QACJ;IACJ;AACJ;AAEA,eAAeO,iBACX3E,UAAsB,EACtBK,UAAmB,EACnBC,SAAqB;IAErB,MAAM,EACFC,eAAe,EACfC,SAAS,EACTC,SAAS,EACTC,UAAU,EACVlB,MAAM,EACNC,iBAAiB,EACjBkB,gBAAgB,EAChBE,KAAK,EACLD,IAAI,EACP,GAAGZ;IAEJ,MAAM4E,UAAU,MAAMC,IAAAA,qBAAY,EAACrE,WAAWD;IAC9CqE,QAAQE,EAAE,CAAC,SAAS;QAChB,IAAIxE,sBAAAA,gCAAAA,UAAWyE,YAAY,EAAE;YACzBzE,UAAUyE,YAAY;QAC1B,OAAO,IAAI,CAAClE,OAAO;YACfoB,QAAQ+C,IAAI,CAAC;QACjB;IACJ;IACAJ,QAAQE,EAAE,CAAC,UAAU,OAAMvF;QACvB,IAAI;YACA,IAAI0F,IAAAA,8BAAqB,EAAC1F,UAAUmB,aAAa;gBAC7C,MAAMvB,OACFQ,IAAAA,cAAO,EAACJ,UAAUC,QAAQC,mBAAmB;gBAEjD,MAAMyF,gBAAgBvF,IAAAA,cAAO,EACzBJ,UACAC,QACAC,mBACA;gBAEJ,MAAM0F,kBAAkB,MAAMjF,IAAAA,aAAM,EAACgF;gBACrC,IAAIC,iBAAiB;oBACjB,MAAMhG,OAAO+F;gBACjB;YACJ,OAAO,IAAIzE,WAAW;gBAClB,MAAMtB,OAAOQ,IAAAA,cAAO,EAACJ,UAAUC,QAAQC;YAC3C;QACJ,EAAE,OAAOsC,KAAU;YACf,IAAIA,IAAIqD,IAAI,KAAK,UAAU;gBACvBnD,QAAQC,KAAK,CAACH,IAAIsD,KAAK;YAC3B;QACJ;IACJ;IACA,KAAK,MAAMC,QAAQ;QAAC;QAAO;KAAS,CAAE;QAClCV,QAAQE,EAAE,CAACQ,MAAM,OAAM/F;YACnB,IAAI0F,IAAAA,8BAAqB,EAAC1F,UAAUmB,aAAa;gBAC7C,MAAMU,QAAQC,QAAQC,MAAM;gBAC5B,MAAMiE,cAAc;oBAChB,MAAM7B,MAAMrC,QAAQC,MAAM,CAACF;oBAC3B,MAAM0C,WAAWJ,GAAG,CAAC,EAAE,GAAG;oBAC1B,OAAOI;gBACX;gBACA,IAAI;oBACA,MAAMlC,SAAS,MAAMC,IAAAA,kBAAa,EAAC;wBAC/BtC;wBACAC;wBACAoB;wBACAZ;wBACAK;wBACAM;oBACJ;oBACA,MAAMmD,WAAWyB;oBACjB,IAAI3D,WAAW/B,wBAAa,CAACgE,QAAQ,EAAE;wBACnC,IAAIvD,sBAAAA,gCAAAA,UAAW2D,SAAS,EAAE;4BACtB3D,UAAU2D,SAAS,CAAC;gCAChBH;gCACAX,UAAU;gCACV5D;4BACJ;wBACJ,OAAO,IAAI,CAACsB,OAAO;4BACfqD,eAAM,CAACC,KAAK,CACRJ,IAAAA,YAAM,EACF,CAAC,sBAAsB,EAAExE,SAAS,kBAAkB,CAAC,EACrDuE,SAASE,OAAO,CAAC;wBAG7B;oBACJ;gBACJ,EAAE,OAAO9B,OAAY;oBACjB,IAAI5B,sBAAAA,gCAAAA,UAAW0B,MAAM,EAAE;wBACnB,MAAMb,UAAU,IAAID;wBACpBC,QAAQW,GAAG,CAACvC,UAAU2C,MAAMC,OAAO;wBACnC7B,UAAU0B,MAAM,CAAC;4BAAE8B,UAAUyB;4BAAepE;wBAAQ;oBACxD,OAAO;wBACHc,QAAQC,KAAK,CAACA,MAAMC,OAAO;oBAC/B;gBACJ;YACJ,OAAO,IAAI1B,WAAW;gBAClB,MAAMW,QAAQC,QAAQC,MAAM;gBAC5B,MAAMiE,cAAc;oBAChB,MAAM7B,MAAMrC,QAAQC,MAAM,CAACF;oBAC3B,MAAM0C,WAAWJ,GAAG,CAAC,EAAE,GAAG;oBAC1B,OAAOI;gBACX;gBACA,IAAI;oBACA,MAAMlC,SAAS,MAAMtC,WACjBC,UACAC,QACAC;oBAEJ,IAAImC,WAAW/B,wBAAa,CAACC,MAAM,EAAE;wBACjC,MAAMgE,WAAWyB;wBACjB,IAAIjF,sBAAAA,gCAAAA,UAAW2D,SAAS,EAAE;4BACtB3D,UAAU2D,SAAS,CAAC;gCAChBH;gCACAV,QAAQ;gCACR7D;4BACJ;wBACJ,OAAO,IAAI,CAACsB,OAAO;4BACfqD,eAAM,CAACC,KAAK,CACRJ,IAAAA,YAAM,EACF,CAAC,oBAAoB,EAAExE,SAAS,kBAAkB,CAAC,EACnDuE,SAASE,OAAO,CAAC;wBAG7B;oBACJ;gBACJ,EAAE,OAAO9B,OAAY;oBACjB,IAAI5B,sBAAAA,gCAAAA,UAAW0B,MAAM,EAAE;wBACnB,MAAMb,UAAU,IAAID;wBACpBC,QAAQW,GAAG,CAACvC,UAAU2C,MAAMC,OAAO;wBACnC7B,UAAU0B,MAAM,CAAC;4BAAE8B,UAAUyB;4BAAepE;wBAAQ;oBACxD,OAAO;wBACHc,QAAQC,KAAK,CAAC,CAAC,eAAe,EAAE3C,SAAS,CAAC;wBAC1C0C,QAAQC,KAAK,CAACA,MAAMC,OAAO;oBAC/B;gBACJ;YACJ;QACJ;IACJ;AACJ;AAEe,eAAerD,IAAI,EAC9BkB,UAAU,EACVK,UAAU,EACVC,SAAS,EAKZ;IACG,MAAM,EAAEQ,KAAK,EAAE,GAAGd;IAElB,MAAMD,uBAAuBC;IAC7B,MAAMI,mBAAmBJ,YAAYK,YAAYC;IAEjD,IAAIQ,OAAO;QACP,MAAM6D,iBAAiB3E,YAAYK,YAAYC;IACnD;AACJ"}