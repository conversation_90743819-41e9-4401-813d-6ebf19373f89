{"name": "find-versions", "version": "5.1.0", "description": "Find semver versions in a string: `unicorn v1.2.3` → `1.2.3`", "license": "MIT", "repository": "sindresorhus/find-versions", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["semver", "version", "versions", "regex", "regexp", "match", "matching", "semantic", "find", "extract", "get"], "dependencies": {"semver-regex": "^4.0.5"}, "devDependencies": {"ava": "^4.3.0", "tsd": "^0.20.0", "xo": "^0.49.0"}}