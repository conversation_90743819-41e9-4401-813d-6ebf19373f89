{"name": "@swc/cli", "version": "0.5.2", "description": "CLI for the swc project", "main": "lib/swc/index.js", "repository": {"type": "git", "url": "https://github.com/swc-project/pkgs.git"}, "keywords": ["swc", "cli", "babel", "es6", "transpile", "transpiler", "compiler", "javascript"], "author": "강동윤 <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/swc-project/pkgs/issues"}, "homepage": "https://github.com/swc-project/pkgs", "engines": {"node": ">= 16.14.0"}, "bin": {"swc": "./bin/swc.js", "swcx": "./bin/swcx.js", "spack": "./bin/spack.js"}, "dependencies": {"@xhmikosr/bin-wrapper": "^13.0.5", "commander": "^8.3.0", "fast-glob": "^3.2.5", "minimatch": "^9.0.3", "piscina": "^4.3.1", "semver": "^7.3.8", "slash": "3.0.0", "source-map": "^0.7.3", "@swc/counter": "^0.1.3"}, "devDependencies": {"@swc/cli": "0.5.2", "@swc/core": "^1.6.4", "@swc/types": "^0.1.9", "@types/jest": "^29.5.0", "@types/node": "^20.11.5", "@types/semver": "^7.3.13", "chokidar": "^3.5.1", "deepmerge": "^4.2.2", "jest": "^29.5.0", "ts-jest": "^29.0.5", "typescript": "~4.3.2", "@swc/jest": "^0.2.37"}, "peerDependencies": {"@swc/core": "^1.2.66", "chokidar": "^3.5.1"}, "peerDependenciesMeta": {"chokidar": {"optional": true}}, "files": ["bin", "lib"], "scripts": {"types": "tsc", "types:watch": "tsc --watch", "build": "swc src --strip-leading-paths -d lib --ignore '**/*.test.ts' --ignore '**/__mocks__/**'", "build:watch": "swc src --strip-leading-paths -d lib --watch --ignore '**/*.test.ts' --ignore '**/__mocks__/**'", "format:src": "prettier --write src", "format:examples": "prettier --write examples", "format": "pnpm format:src && pnpm format:examples", "test": "jest", "test:watch": "jest --watch", "test:coverage": "JEST_COVERAGE=true jest --coverage"}}