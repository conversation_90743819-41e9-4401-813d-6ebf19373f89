{"version": 3, "sources": ["../../src/swcx/index.ts"], "sourcesContent": ["#!/usr/bin/env node\n\nimport minVersion from \"semver/ranges/min-version\";\nimport { existsSync, readFileSync } from \"fs\";\nimport * as path from \"path\";\nimport { spawn, StdioOptions } from \"child_process\";\nconst { BinWrapper } = require(\"@xhmikosr/bin-wrapper\");\n\nconst { platform, arch } = process;\n\nconst SWC_CLI_ENV = {\n    // Allow to specify specific version of swc binary version to use\n    SWCX_CORE_VERSION_OVERRIDE: \"SWCX_CORE_VERSION_OVERRIDE\",\n    // Allow to skip check peer @swc/core version check\n    SWCX_SKIP_CORE_VERSION_CHECK: \"SWCX_SKIP_CORE_VERSION_CHECK\",\n};\n\n/**\n * Determines version of the swc cli binary to use.\n *\n * By default, if cwd have a package.json already have dependency to @swc/core\n * will try to match those versions. Otherwise will use the latest\n * version available when @swc/cli is published.\n *\n * If `SWCX_CORE_VERSION_OVERRIDE` is set, both will be ignored and\n * explicitly will try to use the version specified. Note this won't ceck\n * validity of the version.\n */\nconst getCoreVersion = () => {\n    const latestVersion = \"1.3.24\";\n\n    if (process.env[SWC_CLI_ENV.SWCX_CORE_VERSION_OVERRIDE]) {\n        console.log(\n            `Using swc core version from ${SWC_CLI_ENV.SWCX_CORE_VERSION_OVERRIDE} env variable`\n        );\n        return `${process.env[SWC_CLI_ENV.SWCX_CORE_VERSION_OVERRIDE]}`;\n    }\n\n    try {\n        if (!process.env[SWC_CLI_ENV.SWCX_SKIP_CORE_VERSION_CHECK]) {\n            const cwdPackageManifestPath = path.resolve(\n                process.cwd(),\n                \"package.json\"\n            );\n            if (existsSync(cwdPackageManifestPath)) {\n                const {\n                    dependencies,\n                    devDependencies,\n                } = require(cwdPackageManifestPath);\n                const swcCoreVersion =\n                    dependencies?.[\"@swc/core\"] ||\n                    devDependencies?.[\"@swc/core\"];\n                if (swcCoreVersion) {\n                    return minVersion(swcCoreVersion);\n                }\n            } else {\n                return latestVersion;\n            }\n        } else {\n            console.log(\n                `Skipping swc core version check due to ${SWC_CLI_ENV.SWCX_SKIP_CORE_VERSION_CHECK} env variable`\n            );\n        }\n    } catch (e) {\n        console.warn(\n            `Failed to determine swc core version from package.json, using latest available version ${latestVersion} instead`,\n            e\n        );\n    }\n\n    return latestVersion;\n};\n\nconst isMusl = () =>\n    (() => {\n        function isMusl() {\n            if (\n                !process.report ||\n                typeof process.report.getReport !== \"function\"\n            ) {\n                try {\n                    return readFileSync(\"/usr/bin/ldd\", \"utf8\").includes(\n                        \"musl\"\n                    );\n                } catch (e) {\n                    return true;\n                }\n            } else {\n                const { glibcVersionRuntime } = (\n                    process.report.getReport() as any\n                ).header;\n                return !glibcVersionRuntime;\n            }\n        }\n\n        return isMusl();\n    })();\n\nconst getBinaryName = () => {\n    const platformBinaryMap: Record<string, Partial<Record<string, string>>> = {\n        win32: {\n            x64: \"swc-win32-x64-msvc.exe\",\n            ia32: \"swc-win32-ia32-msvc.exe\",\n            arm64: \"swc-win32-arm64-msvc.exe\",\n        },\n        darwin: {\n            x64: \"swc-darwin-x64\",\n            arm64: \"swc-darwin-arm64\",\n        },\n        linux: {\n            x64: `swc-linux-x64-${isMusl() ? \"musl\" : \"gnu\"}`,\n            arm64: `swc-linux-arm64-${isMusl() ? \"musl\" : \"gnu\"}`,\n            arm: \"swc-linux-arm64-gnu\",\n        },\n    };\n\n    const binaryName = platformBinaryMap[platform][arch];\n\n    if (!binaryName) {\n        throw new Error(\n            `Unsupported platform: binary ${binaryName} for '${platform} ${arch}' is not available`\n        );\n    }\n\n    return binaryName;\n};\n\nconst executeBinary = async () => {\n    const coreVersion = getCoreVersion();\n    const releaseBase = `https://github.com/swc-project/swc/releases/download/v${coreVersion}`;\n    const binaryName = getBinaryName();\n\n    const bin = new BinWrapper({\n        // do not explicitly run the binary to check existence to avoid\n        // redundant spawn\n        skipCheck: true,\n    })\n        .src(`${releaseBase}/${binaryName}`, platform, arch)\n        .dest(`node_modules/.bin/swc-cli-${coreVersion}`)\n        .use(binaryName);\n\n    await bin.run();\n\n    const binPath = bin.path;\n\n    const [, , ...args] = process.argv;\n    const options = { cwd: process.cwd(), stdio: \"inherit\" as StdioOptions };\n\n    return spawn(binPath, args, options);\n};\n\nexecuteBinary().catch(e => console.error(e));\n"], "names": ["BinWrapper", "require", "platform", "arch", "process", "SWC_CLI_ENV", "SWCX_CORE_VERSION_OVERRIDE", "SWCX_SKIP_CORE_VERSION_CHECK", "getCoreVersion", "latestVersion", "env", "console", "log", "cwdPackageManifestPath", "path", "resolve", "cwd", "existsSync", "dependencies", "devDependencies", "swcCoreVersion", "minVersion", "e", "warn", "isMusl", "report", "getReport", "readFileSync", "includes", "glibcVersionRuntime", "header", "getBinaryName", "platformBinaryMap", "win32", "x64", "ia32", "arm64", "darwin", "linux", "arm", "binaryName", "Error", "executeBinary", "coreVersion", "releaseBase", "bin", "<PERSON><PERSON><PERSON><PERSON>", "src", "dest", "use", "run", "<PERSON><PERSON><PERSON>", "args", "argv", "options", "stdio", "spawn", "catch", "error"], "mappings": ";;;;;mEAEuB;oBACkB;8DACnB;+BACc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACpC,MAAM,EAAEA,UAAU,EAAE,GAAGC,QAAQ;AAE/B,MAAM,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC;AAE3B,MAAMC,cAAc;IAChB,iEAAiE;IACjEC,4BAA4B;IAC5B,mDAAmD;IACnDC,8BAA8B;AAClC;AAEA;;;;;;;;;;CAUC,GACD,MAAMC,iBAAiB;IACnB,MAAMC,gBAAgB;IAEtB,IAAIL,QAAQM,GAAG,CAACL,YAAYC,0BAA0B,CAAC,EAAE;QACrDK,QAAQC,GAAG,CACP,CAAC,4BAA4B,EAAEP,YAAYC,0BAA0B,CAAC,aAAa,CAAC;QAExF,OAAO,CAAC,EAAEF,QAAQM,GAAG,CAACL,YAAYC,0BAA0B,CAAC,CAAC,CAAC;IACnE;IAEA,IAAI;QACA,IAAI,CAACF,QAAQM,GAAG,CAACL,YAAYE,4BAA4B,CAAC,EAAE;YACxD,MAAMM,yBAAyBC,MAAKC,OAAO,CACvCX,QAAQY,GAAG,IACX;YAEJ,IAAIC,IAAAA,cAAU,EAACJ,yBAAyB;gBACpC,MAAM,EACFK,YAAY,EACZC,eAAe,EAClB,GAAGlB,QAAQY;gBACZ,MAAMO,iBACFF,CAAAA,yBAAAA,mCAAAA,YAAc,CAAC,YAAY,MAC3BC,4BAAAA,sCAAAA,eAAiB,CAAC,YAAY;gBAClC,IAAIC,gBAAgB;oBAChB,OAAOC,IAAAA,mBAAU,EAACD;gBACtB;YACJ,OAAO;gBACH,OAAOX;YACX;QACJ,OAAO;YACHE,QAAQC,GAAG,CACP,CAAC,uCAAuC,EAAEP,YAAYE,4BAA4B,CAAC,aAAa,CAAC;QAEzG;IACJ,EAAE,OAAOe,GAAG;QACRX,QAAQY,IAAI,CACR,CAAC,uFAAuF,EAAEd,cAAc,QAAQ,CAAC,EACjHa;IAER;IAEA,OAAOb;AACX;AAEA,MAAMe,SAAS,IACX,AAAC,CAAA;QACG,SAASA;YACL,IACI,CAACpB,QAAQqB,MAAM,IACf,OAAOrB,QAAQqB,MAAM,CAACC,SAAS,KAAK,YACtC;gBACE,IAAI;oBACA,OAAOC,IAAAA,gBAAY,EAAC,gBAAgB,QAAQC,QAAQ,CAChD;gBAER,EAAE,OAAON,GAAG;oBACR,OAAO;gBACX;YACJ,OAAO;gBACH,MAAM,EAAEO,mBAAmB,EAAE,GAAG,AAC5BzB,QAAQqB,MAAM,CAACC,SAAS,GAC1BI,MAAM;gBACR,OAAO,CAACD;YACZ;QACJ;QAEA,OAAOL;IACX,CAAA;AAEJ,MAAMO,gBAAgB;IAClB,MAAMC,oBAAqE;QACvEC,OAAO;YACHC,KAAK;YACLC,MAAM;YACNC,OAAO;QACX;QACAC,QAAQ;YACJH,KAAK;YACLE,OAAO;QACX;QACAE,OAAO;YACHJ,KAAK,CAAC,cAAc,EAAEV,WAAW,SAAS,MAAM,CAAC;YACjDY,OAAO,CAAC,gBAAgB,EAAEZ,WAAW,SAAS,MAAM,CAAC;YACrDe,KAAK;QACT;IACJ;IAEA,MAAMC,aAAaR,iBAAiB,CAAC9B,SAAS,CAACC,KAAK;IAEpD,IAAI,CAACqC,YAAY;QACb,MAAM,IAAIC,MACN,CAAC,6BAA6B,EAAED,WAAW,MAAM,EAAEtC,SAAS,CAAC,EAAEC,KAAK,kBAAkB,CAAC;IAE/F;IAEA,OAAOqC;AACX;AAEA,MAAME,gBAAgB;IAClB,MAAMC,cAAcnC;IACpB,MAAMoC,cAAc,CAAC,sDAAsD,EAAED,YAAY,CAAC;IAC1F,MAAMH,aAAaT;IAEnB,MAAMc,MAAM,IAAI7C,WAAW;QACvB,+DAA+D;QAC/D,kBAAkB;QAClB8C,WAAW;IACf,GACKC,GAAG,CAAC,CAAC,EAAEH,YAAY,CAAC,EAAEJ,WAAW,CAAC,EAAEtC,UAAUC,MAC9C6C,IAAI,CAAC,CAAC,0BAA0B,EAAEL,YAAY,CAAC,EAC/CM,GAAG,CAACT;IAET,MAAMK,IAAIK,GAAG;IAEb,MAAMC,UAAUN,IAAI/B,IAAI;IAExB,MAAM,KAAK,GAAGsC,KAAK,GAAGhD,QAAQiD,IAAI;IAClC,MAAMC,UAAU;QAAEtC,KAAKZ,QAAQY,GAAG;QAAIuC,OAAO;IAA0B;IAEvE,OAAOC,IAAAA,oBAAK,EAACL,SAASC,MAAME;AAChC;AAEAZ,gBAAgBe,KAAK,CAACnC,CAAAA,IAAKX,QAAQ+C,KAAK,CAACpC"}