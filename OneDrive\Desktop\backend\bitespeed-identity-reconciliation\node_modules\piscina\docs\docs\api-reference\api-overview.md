---
id: API Overview
sidebar_position: 1
---

| API                                                                    | Description                                                                                                                                                                                                                                                                                         |
| ---------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Class: `Piscina`**                                                   | This class extends [`EventEmitter`](https://nodejs.org/api/events.html#events_class_eventemitter) from Node.js.                                                                                                                                                                                     |
| **Constructor: `new Piscina([options])`**                              | Creates a new instance of the `Piscina` class. [List of available options](./class.md)                                                                                                                                                                                          |
| **Method: `run(task[, options])`**                                     | Schedules a task to be run on a Worker thread.                                                                                                                                                                                                                                                      |
| **Method: `runTask(task[, transferList][, filename][, abortSignal])`** | **Deprecated** -- Use `run(task, options)` instead.                                                                                                                                                                                                                                                 |
| **Method: `destroy()`**                                                | Stops all Workers and rejects all `Promises` for pending tasks.                                                                                                                                                                                                                                     |
| **Method: `close([options])`**                                         | Stops all Workers gracefully.                                                                                                                                                                                                                                                                       |
| **Event: `'error'`**                                                   | Emitted when uncaught exceptions occur inside Worker threads or unexpected messages are sent from Worker threads.                                                                                                                                                                                   |
| **Event: `'drain'`**                                                   | Emitted whenever the `queueSize` reaches `0`.                                                                                                                                                                                                                                                       |
| **Event: `'needsDrain'`**                                              | Triggered once the total capacity of the pool is exceeded by the number of tasks enqueued that are pending execution.                                                                                                                                                                               |
| **Event: `'message'`**                                                 | Emitted whenever a message is received from a worker thread.                                                                                                                                                                                                                                        |
| **Property: `completed` (readonly)**                                   | The current number of completed tasks.                                                                                                                                                                                                                                                              |
| **Property: `duration` (readonly)**                                    | The length of time (in milliseconds) since this `Piscina` instance was created.                                                                                                                                                                                                                     |
| **Property: `options` (readonly)**                                     | A copy of the options that are currently being used by this instance.                                                                                                                                                                                                                               |
| **Property: `runTime` (readonly)**                                     | A histogram summary object summarizing the collected run times of completed tasks.                                                                                                                                                                                                                  |
| **Property: `threads` (readonly)**                                     | An Array of the `Worker` instances used by this pool.                                                                                                                                                                                                                                               |
| **Property: `queueSize` (readonly)**                                   | The current number of tasks waiting to be assigned to a Worker thread.                                                                                                                                                                                                                              |
| **Property: `needsDrain` (readonly)**                                  | Boolean value that specifies whether the capacity of the pool has been exceeded by the number of tasks submitted.                                                                                                                                                                                   |
| **Property: `utilization` (readonly)**                                 | A point-in-time ratio comparing the approximate total mean run time of completed tasks to the total runtime capacity of the pool.                                                                                                                                                                   |
| **Property: `waitTime` (readonly)**                                    | A histogram summary object summarizing the collected times tasks spent waiting in the queue.                                                                                                                                                                                                        |
| **Static Property: `isWorkerThread` (readonly)**                       | Is `true` if this code runs inside a `Piscina` threadpool as a Worker.                                                                                                                                                                                                                              |
| **Static Property: `version` (readonly)**                              | Provides the current version of this library as a semver string.                                                                                                                                                                                                                                    |
| **Static Method: `move(value)`**                                       | By default, any value returned by a worker function will be cloned when returned back to the Piscina pool, even if that object is capable of being transferred. The `Piscina.move()` method can be used to wrap and mark transferable values such that they will be transferred rather than cloned. |
| **Interface: `Transferable`**                                          | Objects may implement the `Transferable` interface to create their own custom transferable objects.                                                                                                                                                                                                 |
