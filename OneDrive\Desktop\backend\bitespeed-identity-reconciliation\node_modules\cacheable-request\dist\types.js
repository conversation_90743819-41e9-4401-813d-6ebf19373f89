// Type definitions for cacheable-request 6.0
// Project: https://github.com/lukechilds/cacheable-request#readme
// Definitions by: BendingBender <https://github.com/BendingBender>
//                 <PERSON> <https://github.com/paul<PERSON>>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped
// TypeScript Version: 2.3
export class RequestError extends Error {
    constructor(error) {
        super(error.message);
        Object.assign(this, error);
    }
}
export class CacheError extends Error {
    constructor(error) {
        super(error.message);
        Object.assign(this, error);
    }
}
//# sourceMappingURL=types.js.map