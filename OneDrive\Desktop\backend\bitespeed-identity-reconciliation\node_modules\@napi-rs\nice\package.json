{"name": "@napi-rs/nice", "version": "1.0.1", "description": "https://linux.die.net/man/2/nice binding for Node.js", "main": "nice.js", "types": "index.d.ts", "repository": {"url": "git+ssh://**************/Brooooooklyn/nice.git", "type": "git"}, "license": "MIT", "keywords": ["napi-rs", "NAPI", "N-API", "Rust", "node-addon", "node-addon-api", "nice"], "files": ["index.d.ts", "index.js", "nice.js"], "napi": {"binaryName": "nice", "targets": ["x86_64-apple-darwin", "aarch64-apple-darwin", "x86_64-unknown-linux-gnu", "x86_64-pc-windows-msvc", "x86_64-unknown-linux-musl", "aarch64-unknown-linux-gnu", "i686-pc-windows-msvc", "armv7-unknown-linux-gnueabihf", "aarch64-linux-android", "x86_64-unknown-freebsd", "aarch64-unknown-linux-musl", "armv7-linux-androideabi", "aarch64-pc-windows-msvc", "powerpc64le-unknown-linux-gnu", "s390x-unknown-linux-gnu", "riscv64gc-unknown-linux-gnu"]}, "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "scripts": {"artifacts": "napi artifacts", "build": "napi build --platform --release", "build:debug": "napi build --platform", "format": "run-p format:prettier format:rs format:toml", "format:prettier": "prettier . -w", "format:toml": "taplo format", "format:rs": "cargo fmt", "lint": "ox<PERSON>", "prepublishOnly": "napi prepublish -t npm", "test": "ava", "version": "napi version"}, "devDependencies": {"@napi-rs/cli": "3.0.0-alpha.62", "@oxc-node/core": "^0.0.15", "@taplo/cli": "^0.7.0", "ava": "^6.1.3", "chalk": "^5.3.0", "husky": "^9.0.11", "lint-staged": "^15.2.7", "npm-run-all2": "^6.2.2", "oxlint": "^0.9.0", "prettier": "^3.3.3", "typescript": "^5.5.3"}, "lint-staged": {"*.@(js|ts|tsx)": ["oxlint --fix"], "*.@(js|ts|tsx|yml|yaml|md|json)": ["prettier --write"], "*.toml": ["taplo format"]}, "ava": {"extensions": {"ts": "module"}, "timeout": "2m", "workerThreads": false, "environmentVariables": {"TS_NODE_PROJECT": "./tsconfig.json"}, "nodeArguments": ["--import", "@oxc-node/core/register"]}, "prettier": {"printWidth": 120, "semi": false, "trailingComma": "all", "singleQuote": true, "arrowParens": "always"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Brooooooklyn"}, "packageManager": "pnpm@9.10.0", "optionalDependencies": {"@napi-rs/nice-darwin-x64": "1.0.1", "@napi-rs/nice-darwin-arm64": "1.0.1", "@napi-rs/nice-linux-x64-gnu": "1.0.1", "@napi-rs/nice-win32-x64-msvc": "1.0.1", "@napi-rs/nice-linux-x64-musl": "1.0.1", "@napi-rs/nice-linux-arm64-gnu": "1.0.1", "@napi-rs/nice-win32-ia32-msvc": "1.0.1", "@napi-rs/nice-linux-arm-gnueabihf": "1.0.1", "@napi-rs/nice-android-arm64": "1.0.1", "@napi-rs/nice-freebsd-x64": "1.0.1", "@napi-rs/nice-linux-arm64-musl": "1.0.1", "@napi-rs/nice-android-arm-eabi": "1.0.1", "@napi-rs/nice-win32-arm64-msvc": "1.0.1", "@napi-rs/nice-linux-ppc64-gnu": "1.0.1", "@napi-rs/nice-linux-s390x-gnu": "1.0.1", "@napi-rs/nice-linux-riscv64-gnu": "1.0.1"}}