{"name": "seek-bzip", "version": "2.0.0", "contributors": ["<PERSON><PERSON> (http://cscott.net)", "<PERSON>", "<PERSON>", "<PERSON> (http://landley.net)"], "description": "a pure-JavaScript Node.JS module for random-access decoding bzip2 data", "main": "./lib/index.js", "repository": {"type": "git", "url": "https://github.com/cscott/seek-bzip.git"}, "license": "MIT", "bin": {"seek-bunzip": "./bin/seek-bunzip", "seek-table": "./bin/seek-bzip-table"}, "directories": {"test": "test"}, "dependencies": {"commander": "^6.0.0"}, "devDependencies": {"fibers": "^5.0.0", "mocha": "^8.1.0"}, "scripts": {"test": "mocha"}}