{"version": 3, "sources": ["../../src/swc/util.ts"], "sourcesContent": ["import * as swc from \"@swc/core\";\nimport slash from \"slash\";\nimport { mkdirSync, writeFileSync, promises } from \"fs\";\nimport { dirname, join, relative } from \"path\";\nimport { stderr } from \"process\";\n\nexport async function exists(path: string): Promise<boolean> {\n    let pathExists = true;\n    try {\n        await promises.access(path);\n    } catch (err: any) {\n        pathExists = false;\n    }\n    return pathExists;\n}\n\nexport async function transform(\n    filename: string,\n    code: string,\n    opts: swc.Options,\n    sync: boolean,\n    outputPath: string | undefined\n): Promise<swc.Output> {\n    opts = {\n        filename,\n        ...opts,\n    };\n\n    if (outputPath) {\n        opts.outputPath = outputPath;\n    }\n\n    if (sync) {\n        return swc.transformSync(code, opts);\n    }\n\n    return swc.transform(code, opts);\n}\n\nexport async function compile(\n    filename: string,\n    opts: swc.Options,\n    sync: boolean,\n    outputPath: string | undefined\n): Promise<swc.Output | void> {\n    opts = {\n        ...opts,\n    };\n    if (outputPath) {\n        opts.outputPath = outputPath;\n    }\n\n    try {\n        const result = sync\n            ? swc.transformFileSync(filename, opts)\n            : await swc.transformFile(filename, opts);\n\n        if (result.map) {\n            // TODO: fix this in core\n            // https://github.com/swc-project/swc/issues/1388\n            const sourceMap = JSON.parse(result.map);\n            if (opts.sourceFileName) {\n                sourceMap[\"sources\"][0] = opts.sourceFileName;\n            }\n            if (opts.sourceRoot) {\n                sourceMap[\"sourceRoot\"] = opts.sourceRoot;\n            }\n            result.map = JSON.stringify(sourceMap);\n        }\n        return result;\n    } catch (err: any) {\n        if (!err.message.includes(\"ignored by .swcrc\")) {\n            throw err;\n        }\n    }\n}\n\nexport function outputFile(\n    output: swc.Output,\n    filename: string,\n    sourceMaps: undefined | swc.Options[\"sourceMaps\"]\n) {\n    const destDir = dirname(filename);\n    mkdirSync(destDir, { recursive: true });\n\n    let code = output.code;\n    if (output.map && sourceMaps !== \"inline\") {\n        // we've requested for a sourcemap to be written to disk\n        const fileDirName = dirname(filename);\n        const mapLoc = filename + \".map\";\n        code +=\n            \"\\n//# sourceMappingURL=\" + slash(relative(fileDirName, mapLoc));\n        writeFileSync(mapLoc, output.map);\n    }\n\n    writeFileSync(filename, code);\n}\n\nexport function assertCompilationResult<T>(\n    result: Map<string, Error | T>,\n    quiet = false\n): asserts result is Map<string, T> {\n    let compiled = 0;\n    let copied = 0;\n    let failed = 0;\n    for (const value of result.values()) {\n        if (value instanceof Error) {\n            failed++;\n        } else if ((value as unknown) === \"copied\") {\n            copied++;\n        } else if (value) {\n            compiled++;\n        }\n    }\n    if (!quiet && compiled + copied > 0) {\n        const copyResult = copied === 0 ? \" \" : ` (copied ${copied}) `;\n        stderr.write(\n            `Successfully compiled ${compiled} ${\n                compiled !== 1 ? \"files\" : \"file\"\n            }${copyResult}with swc.\\n`\n        );\n    }\n\n    if (failed > 0) {\n        throw new Error(\n            `Failed to compile ${failed} ${\n                failed !== 1 ? \"files\" : \"file\"\n            } with swc.`\n        );\n    }\n}\n\nfunction stripComponents(filename: string) {\n    const components = filename.split(\"/\").slice(1);\n    if (!components.length) {\n        return filename;\n    }\n    while (components[0] === \"..\") {\n        components.shift();\n    }\n    return components.join(\"/\");\n}\n\nconst cwd = process.cwd();\n\nexport function getDest(\n    filename: string,\n    outDir: string,\n    stripLeadingPaths: boolean,\n    ext?: string\n) {\n    let base = slash(relative(cwd, filename));\n    if (stripLeadingPaths) {\n        base = stripComponents(base);\n    }\n    if (ext) {\n        base = base.replace(/\\.\\w*$/, ext);\n    }\n    return join(outDir, base);\n}\n"], "names": ["assertCompilationResult", "compile", "exists", "getDest", "outputFile", "transform", "path", "pathExists", "promises", "access", "err", "filename", "code", "opts", "sync", "outputPath", "swc", "transformSync", "result", "transformFileSync", "transformFile", "map", "sourceMap", "JSON", "parse", "sourceFileName", "sourceRoot", "stringify", "message", "includes", "output", "sourceMaps", "destDir", "dirname", "mkdirSync", "recursive", "fileDirName", "mapLoc", "slash", "relative", "writeFileSync", "quiet", "compiled", "copied", "failed", "value", "values", "Error", "copyResult", "stderr", "write", "stripComponents", "components", "split", "slice", "length", "shift", "join", "cwd", "process", "outDir", "stripLeadingPaths", "ext", "base", "replace"], "mappings": ";;;;;;;;;;;IAkGgBA,uBAAuB;eAAvBA;;IA3DMC,OAAO;eAAPA;;IAjCAC,MAAM;eAANA;;IA2INC,OAAO;eAAPA;;IApEAC,UAAU;eAAVA;;IA7DMC,SAAS;eAATA;;;8DAhBD;8DACH;oBACiC;sBACX;yBACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEhB,eAAeH,OAAOI,IAAY;IACrC,IAAIC,aAAa;IACjB,IAAI;QACA,MAAMC,YAAQ,CAACC,MAAM,CAACH;IAC1B,EAAE,OAAOI,KAAU;QACfH,aAAa;IACjB;IACA,OAAOA;AACX;AAEO,eAAeF,UAClBM,QAAgB,EAChBC,IAAY,EACZC,IAAiB,EACjBC,IAAa,EACbC,UAA8B;IAE9BF,OAAO;QACHF;QACA,GAAGE,IAAI;IACX;IAEA,IAAIE,YAAY;QACZF,KAAKE,UAAU,GAAGA;IACtB;IAEA,IAAID,MAAM;QACN,OAAOE,MAAIC,aAAa,CAACL,MAAMC;IACnC;IAEA,OAAOG,MAAIX,SAAS,CAACO,MAAMC;AAC/B;AAEO,eAAeZ,QAClBU,QAAgB,EAChBE,IAAiB,EACjBC,IAAa,EACbC,UAA8B;IAE9BF,OAAO;QACH,GAAGA,IAAI;IACX;IACA,IAAIE,YAAY;QACZF,KAAKE,UAAU,GAAGA;IACtB;IAEA,IAAI;QACA,MAAMG,SAASJ,OACTE,MAAIG,iBAAiB,CAACR,UAAUE,QAChC,MAAMG,MAAII,aAAa,CAACT,UAAUE;QAExC,IAAIK,OAAOG,GAAG,EAAE;YACZ,yBAAyB;YACzB,iDAAiD;YACjD,MAAMC,YAAYC,KAAKC,KAAK,CAACN,OAAOG,GAAG;YACvC,IAAIR,KAAKY,cAAc,EAAE;gBACrBH,SAAS,CAAC,UAAU,CAAC,EAAE,GAAGT,KAAKY,cAAc;YACjD;YACA,IAAIZ,KAAKa,UAAU,EAAE;gBACjBJ,SAAS,CAAC,aAAa,GAAGT,KAAKa,UAAU;YAC7C;YACAR,OAAOG,GAAG,GAAGE,KAAKI,SAAS,CAACL;QAChC;QACA,OAAOJ;IACX,EAAE,OAAOR,KAAU;QACf,IAAI,CAACA,IAAIkB,OAAO,CAACC,QAAQ,CAAC,sBAAsB;YAC5C,MAAMnB;QACV;IACJ;AACJ;AAEO,SAASN,WACZ0B,MAAkB,EAClBnB,QAAgB,EAChBoB,UAAiD;IAEjD,MAAMC,UAAUC,IAAAA,aAAO,EAACtB;IACxBuB,IAAAA,aAAS,EAACF,SAAS;QAAEG,WAAW;IAAK;IAErC,IAAIvB,OAAOkB,OAAOlB,IAAI;IACtB,IAAIkB,OAAOT,GAAG,IAAIU,eAAe,UAAU;QACvC,wDAAwD;QACxD,MAAMK,cAAcH,IAAAA,aAAO,EAACtB;QAC5B,MAAM0B,SAAS1B,WAAW;QAC1BC,QACI,4BAA4B0B,IAAAA,cAAK,EAACC,IAAAA,cAAQ,EAACH,aAAaC;QAC5DG,IAAAA,iBAAa,EAACH,QAAQP,OAAOT,GAAG;IACpC;IAEAmB,IAAAA,iBAAa,EAAC7B,UAAUC;AAC5B;AAEO,SAASZ,wBACZkB,MAA8B,EAC9BuB,QAAQ,KAAK;IAEb,IAAIC,WAAW;IACf,IAAIC,SAAS;IACb,IAAIC,SAAS;IACb,KAAK,MAAMC,SAAS3B,OAAO4B,MAAM,GAAI;QACjC,IAAID,iBAAiBE,OAAO;YACxBH;QACJ,OAAO,IAAI,AAACC,UAAsB,UAAU;YACxCF;QACJ,OAAO,IAAIE,OAAO;YACdH;QACJ;IACJ;IACA,IAAI,CAACD,SAASC,WAAWC,SAAS,GAAG;QACjC,MAAMK,aAAaL,WAAW,IAAI,MAAM,CAAC,SAAS,EAAEA,OAAO,EAAE,CAAC;QAC9DM,eAAM,CAACC,KAAK,CACR,CAAC,sBAAsB,EAAER,SAAS,CAAC,EAC/BA,aAAa,IAAI,UAAU,OAC9B,EAAEM,WAAW,WAAW,CAAC;IAElC;IAEA,IAAIJ,SAAS,GAAG;QACZ,MAAM,IAAIG,MACN,CAAC,kBAAkB,EAAEH,OAAO,CAAC,EACzBA,WAAW,IAAI,UAAU,OAC5B,UAAU,CAAC;IAEpB;AACJ;AAEA,SAASO,gBAAgBxC,QAAgB;IACrC,MAAMyC,aAAazC,SAAS0C,KAAK,CAAC,KAAKC,KAAK,CAAC;IAC7C,IAAI,CAACF,WAAWG,MAAM,EAAE;QACpB,OAAO5C;IACX;IACA,MAAOyC,UAAU,CAAC,EAAE,KAAK,KAAM;QAC3BA,WAAWI,KAAK;IACpB;IACA,OAAOJ,WAAWK,IAAI,CAAC;AAC3B;AAEA,MAAMC,MAAMC,QAAQD,GAAG;AAEhB,SAASvD,QACZQ,QAAgB,EAChBiD,MAAc,EACdC,iBAA0B,EAC1BC,GAAY;IAEZ,IAAIC,OAAOzB,IAAAA,cAAK,EAACC,IAAAA,cAAQ,EAACmB,KAAK/C;IAC/B,IAAIkD,mBAAmB;QACnBE,OAAOZ,gBAAgBY;IAC3B;IACA,IAAID,KAAK;QACLC,OAAOA,KAAKC,OAAO,CAAC,UAAUF;IAClC;IACA,OAAOL,IAAAA,UAAI,EAACG,QAAQG;AACxB"}