{"name": "peek-readable", "version": "5.4.2", "description": "Read and peek from a readable stream", "author": {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Borewit"}, "funding": {"type": "github", "url": "https://github.com/sponsors/Borewit"}, "scripts": {"clean": "del-cli 'lib/**/*.js' 'lib/**/*.js.map' 'lib/**/*.d.ts' 'test/**/*.js' 'test/**/*.js.map' 'coverage' '.nyc_output'", "build": "npm run clean && npm run compile", "compile-src": "tsc -p lib", "compile-test": "tsc -p test", "compile": "yarn run compile-src && yarn run compile-test", "lint-ts": "biome check", "lint-md": "remark -u preset-lint-recommended .", "lint": "yarn run lint-md && yarn run lint-ts", "test": "mocha", "test-coverage": "c8 npm run test", "start": "yarn run compile && yarn run lint && yarn run cover-test"}, "engines": {"node": ">=14.16"}, "repository": {"type": "git", "url": "git+https://github.com/Borewit/peek-readable"}, "license": "MIT", "type": "module", "exports": "./lib/index.js", "types": "lib/index.d.ts", "bugs": {"url": "https://github.com/Borewit/peek-readable/issues"}, "files": ["lib/**/*.js", "lib/**/*.d.ts"], "devDependencies": {"@biomejs/biome": "1.9.4", "@types/chai": "^5.0.1", "@types/chai-as-promised": "^8.0.1", "@types/mocha": "^10.0.10", "@types/node": "^22.10.10", "c8": "^10.1.3", "chai": "^5.1.2", "chai-as-promised": "^8.0.1", "del-cli": "^6.0.0", "mocha": "^11.1.0", "remark-cli": "^12.0.1", "remark-preset-lint-recommended": "^7.0.0", "source-map-support": "^0.5.21", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "keywords": ["readable", "buffer", "stream", "read"], "packageManager": "yarn@4.6.0"}