{"version": 3, "sources": ["../../src/swc/options.ts"], "sourcesContent": ["import commander, { Command, OptionValues } from \"commander\";\nimport { version as swc<PERSON>oreVersion } from \"@swc/core\";\nimport type { Options } from \"@swc/core\";\nimport { readFileSync } from \"fs\";\nimport { resolve } from \"path\";\n\nconst DEFAULT_EXTENSIONS = [\n    \".js\",\n    \".jsx\",\n    \".es6\",\n    \".es\",\n    \".mjs\",\n    \".ts\",\n    \".tsx\",\n    \".cts\",\n    \".mts\",\n];\n\nconst pkg = require(\"../../package.json\");\n\nlet program: Command;\nexport const DEFAULT_OUT_FILE_EXTENSION = \"js\";\n\nexport const initProgram = () => {\n    program = new commander.Command();\n\n    /* istanbul ignore next */\n    if (process.env.NODE_ENV === \"test\") {\n        program.exitOverride();\n    }\n\n    program.option(\n        \"-f, --filename [filename]\",\n        \"filename to use when reading from stdin - this will be used in source-maps, errors etc\"\n    );\n\n    program.option(\"--config-file [path]\", \"Path to a .swcrc file to use\");\n\n    program.option(\n        \"--cli-config-file [path]\",\n        \"Path to a JSON file containing CLI options. \" +\n            \"Options provided directly via command line override the ones in the configuration file.\"\n    );\n\n    program.option(\n        \"--env-name [name]\",\n        \"The name of the 'env' to use when loading configs and plugins. \" +\n            \"Defaults to the value of SWC_ENV, or else NODE_ENV, or else 'development'.\"\n    );\n\n    program.option(\"--no-swcrc\", \"Whether or not to look up .swcrc files\");\n\n    program.option(\n        \"--delete-dir-on-start\",\n        \"Whether or not delete output directory on start\"\n    );\n\n    program.option(\n        \"--ignore [list]\",\n        \"list of glob paths to **not** compile\",\n        collect\n    );\n\n    program.option(\n        \"--only [list]\",\n        \"list of glob paths to **only** compile\",\n        collect\n    );\n\n    program.option(\"-w, --watch\", \"Recompile files on changes\");\n\n    program.option(\"-q, --quiet\", \"Suppress compilation output\");\n\n    program.option(\n        \"-s, --source-maps [true|false|inline|both]\",\n        \"generate source maps\",\n        unstringify\n    );\n\n    program.option(\n        \"--source-map-target [string]\",\n        \"set `file` on returned source map\"\n    );\n\n    program.option(\n        \"--source-file-name [string]\",\n        \"set `sources[0]` on returned source map\"\n    );\n\n    program.option(\n        \"--source-root [filename]\",\n        \"the root from which all sources are relative\"\n    );\n\n    program.option(\n        \"-o, --out-file [out]\",\n        \"Compile all input files into a single file\"\n    );\n\n    program.option(\n        \"-d, --out-dir [out]\",\n        \"Compile an input directory of modules into an output directory\"\n    );\n\n    program.option(\n        \"--out-file-extension [string]\",\n        \"Use a specific extension for the output files [default: js]\",\n        DEFAULT_OUT_FILE_EXTENSION\n    );\n\n    program.option(\n        \"-D, --copy-files\",\n        \"When compiling a directory copy over non-compilable files\"\n    );\n\n    program.option(\n        \"--strip-leading-paths\",\n        \"Remove the leading directory (including all parent relative paths) when building the final output path\",\n        false\n    );\n\n    program.option(\n        \"--include-dotfiles\",\n        \"Include dotfiles when compiling and copying non-compilable files\"\n    );\n\n    program.option(\n        \"-C, --config <config>\",\n        \"Override a config from .swcrc file. e.g. -C module.type=amd -C module.moduleId=hello\",\n        collect\n    );\n\n    program.option(\n        \"--sync\",\n        \"Invoke swc synchronously. Useful for debugging.\",\n        collect\n    );\n\n    program.option(\n        \"--workers [number]\",\n        \"The number of workers to use for parallel processing\"\n    );\n\n    program.option(\n        \"--log-watch-compilation\",\n        \"Log a message when a watched file is successfully compiled\",\n        true\n    );\n\n    program.option(\"--extensions [list]\", \"Use specific extensions\", collect);\n\n    program.version(`\n@swc/cli: ${pkg.version}\n@swc/core: ${swcCoreVersion}\n`);\n\n    program.usage(\"[options] <files ...>\");\n    program.addHelpText(\n        \"beforeAll\",\n        `\n============================================================================================\nBeta version of @swc/cli is now available via 'swcx' command.\nThis'll be a default command for @swc/cli@1.\nPlease give it a try and report any issues at https://github.com/swc-project/swc/issues/4017\n============================================================================================\n\n`\n    );\n};\n\nfunction unstringify(val: string): any {\n    try {\n        return JSON.parse(val);\n    } catch {\n        return val;\n    }\n}\n\nfunction loadCLIConfigFile(\n    program: Command,\n    opts: OptionValues,\n    path: string\n): OptionValues {\n    let configOpts: OptionValues;\n    let contents: string;\n\n    // Parse the JSON file\n    try {\n        contents = readFileSync(resolve(process.cwd(), path), \"utf-8\");\n    } catch (e) {\n        throw new Error(`Cannot read CLI config file \"${path}\".`);\n    }\n\n    try {\n        configOpts = JSON.parse(contents);\n    } catch (e) {\n        throw new Error(\n            `CLI config file \"${path}\" is not a well-formed JSON file.`\n        );\n    }\n\n    // Convert kebab case options in camel case one\n    configOpts = Object.fromEntries(\n        Object.entries(configOpts).map(([key, value]) => {\n            const camelCaseKey = key.replace(/(-[-a-z])/g, (_, m) =>\n                m.substring(1).toUpperCase()\n            );\n            return [camelCaseKey, value];\n        })\n    );\n\n    // Split existing options in default and provided one\n    const defaults = [];\n    const provided = [];\n\n    for (const pair of Object.entries(opts)) {\n        if (program.getOptionValueSource(pair[0]) === \"default\") {\n            defaults.push(pair);\n        } else {\n            provided.push(pair);\n        }\n    }\n\n    // Merge options back with right priority\n    return {\n        ...Object.fromEntries(defaults),\n        ...configOpts,\n        ...Object.fromEntries(provided),\n    };\n}\n\nfunction verifyArgsErrors(errors: string[]): void {\n    if (errors.length) {\n        console.error(\"swc:\");\n        for (const error of errors) {\n            console.error(\"  \" + error);\n        }\n        process.exit(2);\n    }\n}\n\nfunction collect(\n    value: string,\n    previousValue?: string[]\n): string[] | undefined {\n    // If the user passed the option with no value, like \"babel file.js --presets\", do nothing.\n    /* istanbul ignore next */\n    if (typeof value !== \"string\") return previousValue;\n\n    const values = value.split(\",\");\n\n    return previousValue ? previousValue.concat(values) : values;\n}\n\nexport interface CliOptions {\n    readonly outDir: string;\n    readonly outFile: string;\n    readonly stripLeadingPaths: boolean;\n    /**\n     * Invoke swc using transformSync. It's useful for debugging.\n     */\n    readonly sync: boolean;\n    readonly workers: number | undefined;\n    readonly sourceMapTarget?: string;\n    readonly filename: string;\n    readonly filenames: string[];\n    readonly extensions: string[];\n    readonly watch: boolean;\n    readonly copyFiles: boolean;\n    readonly outFileExtension: string;\n    readonly includeDotfiles: boolean;\n    readonly deleteDirOnStart: boolean;\n    readonly quiet: boolean;\n\n    readonly only: string[];\n    readonly ignore: string[];\n}\n\nexport interface Callbacks {\n    readonly onSuccess?: (data: {\n        duration: number;\n        /** count of compiled files */\n        compiled?: number;\n        /** count of copied files */\n        copied?: number;\n        filename?: string;\n    }) => any;\n    readonly onFail?: (data: {\n        duration: number;\n        reasons: Map<string, string>;\n    }) => any;\n    readonly onWatchReady?: () => any;\n}\n\nexport default function parserArgs(args: string[]) {\n    program.parse(args);\n    let opts = program.opts();\n\n    if (opts.cliConfigFile) {\n        try {\n            opts = loadCLIConfigFile(program, opts, opts.cliConfigFile);\n        } catch (e: any) {\n            verifyArgsErrors([e.message]);\n            return;\n        }\n    }\n\n    const filenames = program.args;\n    const errors = [];\n\n    if (opts.outDir && !filenames.length) {\n        errors.push(\"--out-dir requires filenames\");\n    }\n\n    if (opts.outFile && opts.outDir) {\n        errors.push(\"--out-file and --out-dir cannot be used together\");\n    }\n\n    if (opts.watch) {\n        if (!opts.outFile && !opts.outDir) {\n            errors.push(\"--watch requires --out-file or --out-dir\");\n        }\n\n        if (!filenames.length) {\n            errors.push(\"--watch requires filenames\");\n        }\n    }\n\n    if (\n        !opts.outDir &&\n        filenames.length === 0 &&\n        typeof opts.filename !== \"string\" &&\n        opts.swcrc !== false\n    ) {\n        errors.push(\n            \"stdin compilation requires either -f/--filename [filename] or --no-swcrc\"\n        );\n    }\n\n    let workers: number | undefined;\n    if (opts.workers != null) {\n        workers = parseFloat(opts.workers);\n        if (!Number.isInteger(workers) || workers < 0) {\n            errors.push(\n                \"--workers must be a positive integer (found \" +\n                    opts.workers +\n                    \")\"\n            );\n        }\n    }\n\n    verifyArgsErrors(errors);\n\n    const swcOptions: Options = {\n        jsc: {\n            parser: undefined,\n            transform: {},\n        },\n        sourceFileName: opts.sourceFileName,\n        sourceRoot: opts.sourceRoot,\n        configFile: opts.configFile,\n        swcrc: opts.swcrc,\n    };\n\n    if (opts.sourceMaps !== undefined) {\n        swcOptions.sourceMaps = opts.sourceMaps;\n    }\n\n    if (opts.config) {\n        for (const cfg of opts.config as string[]) {\n            const i = cfg.indexOf(\"=\");\n            let key: string;\n            let value: any;\n            if (i === -1) {\n                key = cfg;\n                value = true;\n            } else {\n                key = cfg.substring(0, i);\n                value = unstringify(cfg.substring(i + 1));\n            }\n            // https://github.com/swc-project/cli/issues/45\n            let options = swcOptions as { [key: string]: any };\n            const keyParts = key.split(\".\");\n            const lastIndex = keyParts.length - 1;\n            for (const [index, keyPart] of keyParts.entries()) {\n                if (options[keyPart] === undefined && index !== lastIndex) {\n                    options[keyPart] = {};\n                }\n                if (index === lastIndex) {\n                    options[keyPart] = value;\n                } else {\n                    options = options[keyPart];\n                }\n            }\n        }\n    }\n\n    const cliOptions: CliOptions = {\n        outDir: opts.outDir,\n        outFile: opts.outFile,\n        stripLeadingPaths: Boolean(opts.stripLeadingPaths),\n        filename: opts.filename,\n        filenames,\n        sync: !!opts.sync,\n        workers,\n        sourceMapTarget: opts.sourceMapTarget,\n        extensions: opts.extensions || DEFAULT_EXTENSIONS,\n        watch: !!opts.watch,\n        copyFiles: !!opts.copyFiles,\n        outFileExtension: opts.outFileExtension || DEFAULT_OUT_FILE_EXTENSION,\n        includeDotfiles: !!opts.includeDotfiles,\n        deleteDirOnStart: Boolean(opts.deleteDirOnStart),\n        quiet: !!opts.quiet,\n        only: opts.only || [],\n        ignore: opts.ignore || [],\n    };\n    return {\n        swcOptions,\n        cliOptions,\n    };\n}\n"], "names": ["DEFAULT_OUT_FILE_EXTENSION", "parser<PERSON>rgs", "initProgram", "DEFAULT_EXTENSIONS", "pkg", "require", "program", "commander", "Command", "process", "env", "NODE_ENV", "exitOverride", "option", "collect", "unstringify", "version", "swcCoreVersion", "usage", "addHelpText", "val", "JSON", "parse", "loadCLIConfigFile", "opts", "path", "configOpts", "contents", "readFileSync", "resolve", "cwd", "e", "Error", "Object", "fromEntries", "entries", "map", "key", "value", "camelCase<PERSON>ey", "replace", "_", "m", "substring", "toUpperCase", "defaults", "provided", "pair", "getOptionValueSource", "push", "verifyArgsErrors", "errors", "length", "console", "error", "exit", "previousValue", "values", "split", "concat", "args", "cliConfigFile", "message", "filenames", "outDir", "outFile", "watch", "filename", "swcrc", "workers", "parseFloat", "Number", "isInteger", "swcOptions", "jsc", "parser", "undefined", "transform", "sourceFileName", "sourceRoot", "configFile", "sourceMaps", "config", "cfg", "i", "indexOf", "options", "keyParts", "lastIndex", "index", "key<PERSON><PERSON>", "cliOptions", "stripLeadingPaths", "Boolean", "sync", "sourceMapTarget", "extensions", "copyFiles", "outFileExtension", "includeDotfiles", "deleteDirOnStart", "quiet", "only", "ignore"], "mappings": ";;;;;;;;;;;IAqBaA,0BAA0B;eAA1BA;;IAiRb,OA8HC;eA9HuBC;;IA/QXC,WAAW;eAAXA;;;kEAvBoC;sBACP;oBAEb;sBACL;;;;;;AAExB,MAAMC,qBAAqB;IACvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AAED,MAAMC,MAAMC,QAAQ;AAEpB,IAAIC;AACG,MAAMN,6BAA6B;AAEnC,MAAME,cAAc;IACvBI,UAAU,IAAIC,kBAAS,CAACC,OAAO;IAE/B,wBAAwB,GACxB,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,QAAQ;QACjCL,QAAQM,YAAY;IACxB;IAEAN,QAAQO,MAAM,CACV,6BACA;IAGJP,QAAQO,MAAM,CAAC,wBAAwB;IAEvCP,QAAQO,MAAM,CACV,4BACA,iDACI;IAGRP,QAAQO,MAAM,CACV,qBACA,oEACI;IAGRP,QAAQO,MAAM,CAAC,cAAc;IAE7BP,QAAQO,MAAM,CACV,yBACA;IAGJP,QAAQO,MAAM,CACV,mBACA,yCACAC;IAGJR,QAAQO,MAAM,CACV,iBACA,0CACAC;IAGJR,QAAQO,MAAM,CAAC,eAAe;IAE9BP,QAAQO,MAAM,CAAC,eAAe;IAE9BP,QAAQO,MAAM,CACV,8CACA,wBACAE;IAGJT,QAAQO,MAAM,CACV,gCACA;IAGJP,QAAQO,MAAM,CACV,+BACA;IAGJP,QAAQO,MAAM,CACV,4BACA;IAGJP,QAAQO,MAAM,CACV,wBACA;IAGJP,QAAQO,MAAM,CACV,uBACA;IAGJP,QAAQO,MAAM,CACV,iCACA,+DACAb;IAGJM,QAAQO,MAAM,CACV,oBACA;IAGJP,QAAQO,MAAM,CACV,yBACA,0GACA;IAGJP,QAAQO,MAAM,CACV,sBACA;IAGJP,QAAQO,MAAM,CACV,yBACA,wFACAC;IAGJR,QAAQO,MAAM,CACV,UACA,mDACAC;IAGJR,QAAQO,MAAM,CACV,sBACA;IAGJP,QAAQO,MAAM,CACV,2BACA,8DACA;IAGJP,QAAQO,MAAM,CAAC,uBAAuB,2BAA2BC;IAEjER,QAAQU,OAAO,CAAC,CAAC;UACX,EAAEZ,IAAIY,OAAO,CAAC;WACb,EAAEC,aAAc,CAAC;AAC5B,CAAC;IAEGX,QAAQY,KAAK,CAAC;IACdZ,QAAQa,WAAW,CACf,aACA,CAAC;;;;;;;AAOT,CAAC;AAED;AAEA,SAASJ,YAAYK,GAAW;IAC5B,IAAI;QACA,OAAOC,KAAKC,KAAK,CAACF;IACtB,EAAE,OAAM;QACJ,OAAOA;IACX;AACJ;AAEA,SAASG,kBACLjB,OAAgB,EAChBkB,IAAkB,EAClBC,IAAY;IAEZ,IAAIC;IACJ,IAAIC;IAEJ,sBAAsB;IACtB,IAAI;QACAA,WAAWC,IAAAA,gBAAY,EAACC,IAAAA,aAAO,EAACpB,QAAQqB,GAAG,IAAIL,OAAO;IAC1D,EAAE,OAAOM,GAAG;QACR,MAAM,IAAIC,MAAM,CAAC,6BAA6B,EAAEP,KAAK,EAAE,CAAC;IAC5D;IAEA,IAAI;QACAC,aAAaL,KAAKC,KAAK,CAACK;IAC5B,EAAE,OAAOI,GAAG;QACR,MAAM,IAAIC,MACN,CAAC,iBAAiB,EAAEP,KAAK,iCAAiC,CAAC;IAEnE;IAEA,+CAA+C;IAC/CC,aAAaO,OAAOC,WAAW,CAC3BD,OAAOE,OAAO,CAACT,YAAYU,GAAG,CAAC,CAAC,CAACC,KAAKC,MAAM;QACxC,MAAMC,eAAeF,IAAIG,OAAO,CAAC,cAAc,CAACC,GAAGC,IAC/CA,EAAEC,SAAS,CAAC,GAAGC,WAAW;QAE9B,OAAO;YAACL;YAAcD;SAAM;IAChC;IAGJ,qDAAqD;IACrD,MAAMO,WAAW,EAAE;IACnB,MAAMC,WAAW,EAAE;IAEnB,KAAK,MAAMC,QAAQd,OAAOE,OAAO,CAACX,MAAO;QACrC,IAAIlB,QAAQ0C,oBAAoB,CAACD,IAAI,CAAC,EAAE,MAAM,WAAW;YACrDF,SAASI,IAAI,CAACF;QAClB,OAAO;YACHD,SAASG,IAAI,CAACF;QAClB;IACJ;IAEA,yCAAyC;IACzC,OAAO;QACH,GAAGd,OAAOC,WAAW,CAACW,SAAS;QAC/B,GAAGnB,UAAU;QACb,GAAGO,OAAOC,WAAW,CAACY,SAAS;IACnC;AACJ;AAEA,SAASI,iBAAiBC,MAAgB;IACtC,IAAIA,OAAOC,MAAM,EAAE;QACfC,QAAQC,KAAK,CAAC;QACd,KAAK,MAAMA,SAASH,OAAQ;YACxBE,QAAQC,KAAK,CAAC,OAAOA;QACzB;QACA7C,QAAQ8C,IAAI,CAAC;IACjB;AACJ;AAEA,SAASzC,QACLwB,KAAa,EACbkB,aAAwB;IAExB,2FAA2F;IAC3F,wBAAwB,GACxB,IAAI,OAAOlB,UAAU,UAAU,OAAOkB;IAEtC,MAAMC,SAASnB,MAAMoB,KAAK,CAAC;IAE3B,OAAOF,gBAAgBA,cAAcG,MAAM,CAACF,UAAUA;AAC1D;AA0Ce,SAASxD,WAAW2D,IAAc;IAC7CtD,QAAQgB,KAAK,CAACsC;IACd,IAAIpC,OAAOlB,QAAQkB,IAAI;IAEvB,IAAIA,KAAKqC,aAAa,EAAE;QACpB,IAAI;YACArC,OAAOD,kBAAkBjB,SAASkB,MAAMA,KAAKqC,aAAa;QAC9D,EAAE,OAAO9B,GAAQ;YACbmB,iBAAiB;gBAACnB,EAAE+B,OAAO;aAAC;YAC5B;QACJ;IACJ;IAEA,MAAMC,YAAYzD,QAAQsD,IAAI;IAC9B,MAAMT,SAAS,EAAE;IAEjB,IAAI3B,KAAKwC,MAAM,IAAI,CAACD,UAAUX,MAAM,EAAE;QAClCD,OAAOF,IAAI,CAAC;IAChB;IAEA,IAAIzB,KAAKyC,OAAO,IAAIzC,KAAKwC,MAAM,EAAE;QAC7Bb,OAAOF,IAAI,CAAC;IAChB;IAEA,IAAIzB,KAAK0C,KAAK,EAAE;QACZ,IAAI,CAAC1C,KAAKyC,OAAO,IAAI,CAACzC,KAAKwC,MAAM,EAAE;YAC/Bb,OAAOF,IAAI,CAAC;QAChB;QAEA,IAAI,CAACc,UAAUX,MAAM,EAAE;YACnBD,OAAOF,IAAI,CAAC;QAChB;IACJ;IAEA,IACI,CAACzB,KAAKwC,MAAM,IACZD,UAAUX,MAAM,KAAK,KACrB,OAAO5B,KAAK2C,QAAQ,KAAK,YACzB3C,KAAK4C,KAAK,KAAK,OACjB;QACEjB,OAAOF,IAAI,CACP;IAER;IAEA,IAAIoB;IACJ,IAAI7C,KAAK6C,OAAO,IAAI,MAAM;QACtBA,UAAUC,WAAW9C,KAAK6C,OAAO;QACjC,IAAI,CAACE,OAAOC,SAAS,CAACH,YAAYA,UAAU,GAAG;YAC3ClB,OAAOF,IAAI,CACP,iDACIzB,KAAK6C,OAAO,GACZ;QAEZ;IACJ;IAEAnB,iBAAiBC;IAEjB,MAAMsB,aAAsB;QACxBC,KAAK;YACDC,QAAQC;YACRC,WAAW,CAAC;QAChB;QACAC,gBAAgBtD,KAAKsD,cAAc;QACnCC,YAAYvD,KAAKuD,UAAU;QAC3BC,YAAYxD,KAAKwD,UAAU;QAC3BZ,OAAO5C,KAAK4C,KAAK;IACrB;IAEA,IAAI5C,KAAKyD,UAAU,KAAKL,WAAW;QAC/BH,WAAWQ,UAAU,GAAGzD,KAAKyD,UAAU;IAC3C;IAEA,IAAIzD,KAAK0D,MAAM,EAAE;QACb,KAAK,MAAMC,OAAO3D,KAAK0D,MAAM,CAAc;YACvC,MAAME,IAAID,IAAIE,OAAO,CAAC;YACtB,IAAIhD;YACJ,IAAIC;YACJ,IAAI8C,MAAM,CAAC,GAAG;gBACV/C,MAAM8C;gBACN7C,QAAQ;YACZ,OAAO;gBACHD,MAAM8C,IAAIxC,SAAS,CAAC,GAAGyC;gBACvB9C,QAAQvB,YAAYoE,IAAIxC,SAAS,CAACyC,IAAI;YAC1C;YACA,+CAA+C;YAC/C,IAAIE,UAAUb;YACd,MAAMc,WAAWlD,IAAIqB,KAAK,CAAC;YAC3B,MAAM8B,YAAYD,SAASnC,MAAM,GAAG;YACpC,KAAK,MAAM,CAACqC,OAAOC,QAAQ,IAAIH,SAASpD,OAAO,GAAI;gBAC/C,IAAImD,OAAO,CAACI,QAAQ,KAAKd,aAAaa,UAAUD,WAAW;oBACvDF,OAAO,CAACI,QAAQ,GAAG,CAAC;gBACxB;gBACA,IAAID,UAAUD,WAAW;oBACrBF,OAAO,CAACI,QAAQ,GAAGpD;gBACvB,OAAO;oBACHgD,UAAUA,OAAO,CAACI,QAAQ;gBAC9B;YACJ;QACJ;IACJ;IAEA,MAAMC,aAAyB;QAC3B3B,QAAQxC,KAAKwC,MAAM;QACnBC,SAASzC,KAAKyC,OAAO;QACrB2B,mBAAmBC,QAAQrE,KAAKoE,iBAAiB;QACjDzB,UAAU3C,KAAK2C,QAAQ;QACvBJ;QACA+B,MAAM,CAAC,CAACtE,KAAKsE,IAAI;QACjBzB;QACA0B,iBAAiBvE,KAAKuE,eAAe;QACrCC,YAAYxE,KAAKwE,UAAU,IAAI7F;QAC/B+D,OAAO,CAAC,CAAC1C,KAAK0C,KAAK;QACnB+B,WAAW,CAAC,CAACzE,KAAKyE,SAAS;QAC3BC,kBAAkB1E,KAAK0E,gBAAgB,IAAIlG;QAC3CmG,iBAAiB,CAAC,CAAC3E,KAAK2E,eAAe;QACvCC,kBAAkBP,QAAQrE,KAAK4E,gBAAgB;QAC/CC,OAAO,CAAC,CAAC7E,KAAK6E,KAAK;QACnBC,MAAM9E,KAAK8E,IAAI,IAAI,EAAE;QACrBC,QAAQ/E,KAAK+E,MAAM,IAAI,EAAE;IAC7B;IACA,OAAO;QACH9B;QACAkB;IACJ;AACJ"}