{"version": 3, "sources": ["../../src/swc/dirWorker.ts"], "sourcesContent": ["import slash from \"slash\";\nimport { dirname, relative } from \"path\";\nimport { CompileStatus } from \"./constants\";\nimport { compile, getDest } from \"./util\";\nimport { outputResult } from \"./compile\";\n\nimport type { Options } from \"@swc/core\";\nimport type { CliOptions } from \"./options\";\nimport { DEFAULT_OUT_FILE_EXTENSION } from \"./options\";\n\nexport default async function handleCompile(opts: {\n    filename: string;\n    outDir: string;\n    sync: boolean;\n    cliOptions: CliOptions;\n    swcOptions: Options;\n    outFileExtension?: string;\n}) {\n    const dest = getDest(\n        opts.filename,\n        opts.outDir,\n        opts.cliOptions.stripLeadingPaths,\n        `.${opts.outFileExtension ?? DEFAULT_OUT_FILE_EXTENSION}`\n    );\n    const sourceFileName = slash(relative(dirname(dest), opts.filename));\n\n    const options = { ...opts.swcOptions, sourceFileName };\n\n    const result = await compile(opts.filename, options, opts.sync, dest);\n\n    if (result) {\n        const destDts = getDest(\n            opts.filename,\n            opts.outDir,\n            opts.cliOptions.stripLeadingPaths,\n            `.d.ts`\n        );\n        const destSourcemap = dest + \".map\";\n        await outputResult({\n            output: result,\n            sourceFile: opts.filename,\n            destFile: dest,\n            destDtsFile: destDts,\n            destSourcemapFile: destSourcemap,\n            options,\n        });\n        return CompileStatus.Compiled;\n    } else {\n        return CompileStatus.Omitted;\n    }\n}\n"], "names": ["handleCompile", "opts", "dest", "getDest", "filename", "outDir", "cliOptions", "stripLeadingPaths", "outFileExtension", "DEFAULT_OUT_FILE_EXTENSION", "sourceFileName", "slash", "relative", "dirname", "options", "swcOptions", "result", "compile", "sync", "destDts", "destSourcemap", "outputResult", "output", "sourceFile", "destFile", "destDtsFile", "destSourcemapFile", "CompileStatus", "Compiled", "Omitted"], "mappings": ";;;;+BAUA;;;eAA8BA;;;8DAVZ;sBACgB;2BACJ;sBACG;yBACJ;yBAIc;;;;;;AAE5B,eAAeA,cAAcC,IAO3C;QAKWA;IAJR,MAAMC,OAAOC,IAAAA,aAAO,EAChBF,KAAKG,QAAQ,EACbH,KAAKI,MAAM,EACXJ,KAAKK,UAAU,CAACC,iBAAiB,EACjC,CAAC,CAAC,EAAEN,CAAAA,yBAAAA,KAAKO,gBAAgB,cAArBP,oCAAAA,yBAAyBQ,mCAA0B,CAAC,CAAC;IAE7D,MAAMC,iBAAiBC,IAAAA,cAAK,EAACC,IAAAA,cAAQ,EAACC,IAAAA,aAAO,EAACX,OAAOD,KAAKG,QAAQ;IAElE,MAAMU,UAAU;QAAE,GAAGb,KAAKc,UAAU;QAAEL;IAAe;IAErD,MAAMM,SAAS,MAAMC,IAAAA,aAAO,EAAChB,KAAKG,QAAQ,EAAEU,SAASb,KAAKiB,IAAI,EAAEhB;IAEhE,IAAIc,QAAQ;QACR,MAAMG,UAAUhB,IAAAA,aAAO,EACnBF,KAAKG,QAAQ,EACbH,KAAKI,MAAM,EACXJ,KAAKK,UAAU,CAACC,iBAAiB,EACjC,CAAC,KAAK,CAAC;QAEX,MAAMa,gBAAgBlB,OAAO;QAC7B,MAAMmB,IAAAA,qBAAY,EAAC;YACfC,QAAQN;YACRO,YAAYtB,KAAKG,QAAQ;YACzBoB,UAAUtB;YACVuB,aAAaN;YACbO,mBAAmBN;YACnBN;QACJ;QACA,OAAOa,wBAAa,CAACC,QAAQ;IACjC,OAAO;QACH,OAAOD,wBAAa,CAACE,OAAO;IAChC;AACJ"}