0000000000000000000000000000000000000000 9264c33fb9de64f3213db83150a8cf48a886aead Yash <<EMAIL>> 1748608173 +0530	commit (initial): feat:master initialize NestJS application with basic structure and configuration
9264c33fb9de64f3213db83150a8cf48a886aead 0000000000000000000000000000000000000000 Yash <<EMAIL>> 1748608368 +0530	Branch: renamed refs/heads/master to refs/heads/master
9264c33fb9de64f3213db83150a8cf48a886aead 9264c33fb9de64f3213db83150a8cf48a886aead Yash <<EMAIL>> 1748608368 +0530	Branch: renamed refs/heads/master to refs/heads/master
9264c33fb9de64f3213db83150a8cf48a886aead 9264c33fb9de64f3213db83150a8cf48a886aead Yash <<EMAIL>> 1748608728 +0530	checkout: moving from master to feat/swc
9264c33fb9de64f3213db83150a8cf48a886aead 559b6c7fff4f4b46a2825c5c6b74c888718a9133 Yash <<EMAIL>> 1748609096 +0530	commit: feat:feat/swc Add SWC compiler support for improved build performance
559b6c7fff4f4b46a2825c5c6b74c888718a9133 9264c33fb9de64f3213db83150a8cf48a886aead Yash <<EMAIL>> 1748609171 +0530	checkout: moving from feat/swc to master
9264c33fb9de64f3213db83150a8cf48a886aead d95d3d7df31c242793342aa1006b9bb676632de2 Yash <<EMAIL>> 1748609181 +0530	pull origin master: Fast-forward
d95d3d7df31c242793342aa1006b9bb676632de2 d95d3d7df31c242793342aa1006b9bb676632de2 Yash <<EMAIL>> 1748609272 +0530	checkout: moving from master to fix/prettier-endofline
d95d3d7df31c242793342aa1006b9bb676632de2 7e5c96d4c5004b165a96e4080a002c28a7138608 Yash <<EMAIL>> 1748609414 +0530	commit: fix: standardize prettier config and end-of-line handling
7e5c96d4c5004b165a96e4080a002c28a7138608 d95d3d7df31c242793342aa1006b9bb676632de2 Yash <<EMAIL>> 1748609881 +0530	checkout: moving from fix/prettier-endofline to master
d95d3d7df31c242793342aa1006b9bb676632de2 1ddf1d551f519467354396d3226a06755aa590d8 Yash <<EMAIL>> 1748609890 +0530	pull origin master: Fast-forward
