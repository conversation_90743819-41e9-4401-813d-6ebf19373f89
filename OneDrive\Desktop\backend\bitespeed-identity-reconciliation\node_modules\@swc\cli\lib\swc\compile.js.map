{"version": 3, "sources": ["../../src/swc/compile.ts"], "sourcesContent": ["import slash from \"slash\";\nimport { promises } from \"fs\";\nimport { dirname, relative } from \"path\";\nimport { transformFile, transformFileSync } from \"@swc/core\";\nimport type { Options, Output } from \"@swc/core\";\n\nconst { mkdir, stat, writeFile } = promises;\n\nfunction withSourceMap(\n    output: Output,\n    options: Options,\n    sourceMapFile: string,\n    destDir: string\n) {\n    let dts: string | undefined;\n\n    // TODO: Remove once fixed in core\n    if ((output as any).output) {\n        const json = JSON.parse((output as any).output);\n\n        if (json.__swc_isolated_declarations__) {\n            dts = json.__swc_isolated_declarations__;\n        }\n    }\n\n    if (!output.map || options.sourceMaps === \"inline\") {\n        return {\n            sourceCode: output.code,\n            dts,\n        };\n    }\n    // TODO: remove once fixed in core https://github.com/swc-project/swc/issues/1388\n    const sourceMap = JSON.parse(output.map);\n    if (options.sourceFileName) {\n        sourceMap[\"sources\"][0] = options.sourceFileName;\n    }\n    if (options.sourceRoot) {\n        sourceMap[\"sourceRoot\"] = options.sourceRoot;\n    }\n    output.map = JSON.stringify(sourceMap);\n\n    output.code += `\\n//# sourceMappingURL=${slash(\n        relative(destDir, sourceMapFile)\n    )}`;\n\n    return {\n        sourceMap: output.map,\n        sourceCode: output.code,\n        dts,\n    };\n}\n\nexport async function outputResult({\n    output,\n    sourceFile,\n    destFile,\n    destDtsFile,\n    destSourcemapFile,\n    options,\n}: {\n    output: Output;\n    sourceFile: string;\n    destFile: string;\n    destDtsFile: string;\n    destSourcemapFile: string;\n    options: Options;\n}) {\n    const destDir = dirname(destFile);\n\n    const { sourceMap, sourceCode, dts } = withSourceMap(\n        output,\n        options,\n        destSourcemapFile,\n        destDir\n    );\n\n    await mkdir(destDir, { recursive: true });\n    const { mode } = await stat(sourceFile);\n\n    const dtsPromise = dts\n        ? writeFile(destDtsFile, dts, { mode })\n        : Promise.resolve();\n    const sourceMapPromise = sourceMap\n        ? writeFile(destSourcemapFile, sourceMap, { mode })\n        : Promise.resolve();\n\n    await Promise.all([\n        writeFile(destFile, sourceCode, { mode }),\n        dtsPromise,\n        sourceMapPromise,\n    ]);\n}\n\nexport async function compile(\n    filename: string,\n    opts: Options,\n    sync: boolean,\n    outputPath: string | undefined\n): Promise<Output | void> {\n    const options = { ...opts };\n    if (outputPath) {\n        options.outputPath = outputPath;\n    }\n\n    try {\n        const result = sync\n            ? transformFileSync(filename, options)\n            : await transformFile(filename, options);\n\n        return result;\n    } catch (err: any) {\n        if (!err.message.includes(\"ignored by .swcrc\")) {\n            throw err;\n        }\n    }\n}\n"], "names": ["compile", "outputResult", "mkdir", "stat", "writeFile", "promises", "withSourceMap", "output", "options", "sourceMapFile", "destDir", "dts", "json", "JSON", "parse", "__swc_isolated_declarations__", "map", "sourceMaps", "sourceCode", "code", "sourceMap", "sourceFileName", "sourceRoot", "stringify", "slash", "relative", "sourceFile", "destFile", "destDtsFile", "destSourcemapFile", "dirname", "recursive", "mode", "dtsPromise", "Promise", "resolve", "sourceMapPromise", "all", "filename", "opts", "sync", "outputPath", "result", "transformFileSync", "transformFile", "err", "message", "includes"], "mappings": ";;;;;;;;;;;IA6FsBA,OAAO;eAAPA;;IAzCAC,YAAY;eAAZA;;;8DApDJ;oBACO;sBACS;sBACe;;;;;;AAGjD,MAAM,EAAEC,KAAK,EAAEC,IAAI,EAAEC,SAAS,EAAE,GAAGC,YAAQ;AAE3C,SAASC,cACLC,MAAc,EACdC,OAAgB,EAChBC,aAAqB,EACrBC,OAAe;IAEf,IAAIC;IAEJ,kCAAkC;IAClC,IAAI,AAACJ,OAAeA,MAAM,EAAE;QACxB,MAAMK,OAAOC,KAAKC,KAAK,CAAC,AAACP,OAAeA,MAAM;QAE9C,IAAIK,KAAKG,6BAA6B,EAAE;YACpCJ,MAAMC,KAAKG,6BAA6B;QAC5C;IACJ;IAEA,IAAI,CAACR,OAAOS,GAAG,IAAIR,QAAQS,UAAU,KAAK,UAAU;QAChD,OAAO;YACHC,YAAYX,OAAOY,IAAI;YACvBR;QACJ;IACJ;IACA,iFAAiF;IACjF,MAAMS,YAAYP,KAAKC,KAAK,CAACP,OAAOS,GAAG;IACvC,IAAIR,QAAQa,cAAc,EAAE;QACxBD,SAAS,CAAC,UAAU,CAAC,EAAE,GAAGZ,QAAQa,cAAc;IACpD;IACA,IAAIb,QAAQc,UAAU,EAAE;QACpBF,SAAS,CAAC,aAAa,GAAGZ,QAAQc,UAAU;IAChD;IACAf,OAAOS,GAAG,GAAGH,KAAKU,SAAS,CAACH;IAE5Bb,OAAOY,IAAI,IAAI,CAAC,uBAAuB,EAAEK,IAAAA,cAAK,EAC1CC,IAAAA,cAAQ,EAACf,SAASD,gBACpB,CAAC;IAEH,OAAO;QACHW,WAAWb,OAAOS,GAAG;QACrBE,YAAYX,OAAOY,IAAI;QACvBR;IACJ;AACJ;AAEO,eAAeV,aAAa,EAC/BM,MAAM,EACNmB,UAAU,EACVC,QAAQ,EACRC,WAAW,EACXC,iBAAiB,EACjBrB,OAAO,EAQV;IACG,MAAME,UAAUoB,IAAAA,aAAO,EAACH;IAExB,MAAM,EAAEP,SAAS,EAAEF,UAAU,EAAEP,GAAG,EAAE,GAAGL,cACnCC,QACAC,SACAqB,mBACAnB;IAGJ,MAAMR,MAAMQ,SAAS;QAAEqB,WAAW;IAAK;IACvC,MAAM,EAAEC,IAAI,EAAE,GAAG,MAAM7B,KAAKuB;IAE5B,MAAMO,aAAatB,MACbP,UAAUwB,aAAajB,KAAK;QAAEqB;IAAK,KACnCE,QAAQC,OAAO;IACrB,MAAMC,mBAAmBhB,YACnBhB,UAAUyB,mBAAmBT,WAAW;QAAEY;IAAK,KAC/CE,QAAQC,OAAO;IAErB,MAAMD,QAAQG,GAAG,CAAC;QACdjC,UAAUuB,UAAUT,YAAY;YAAEc;QAAK;QACvCC;QACAG;KACH;AACL;AAEO,eAAepC,QAClBsC,QAAgB,EAChBC,IAAa,EACbC,IAAa,EACbC,UAA8B;IAE9B,MAAMjC,UAAU;QAAE,GAAG+B,IAAI;IAAC;IAC1B,IAAIE,YAAY;QACZjC,QAAQiC,UAAU,GAAGA;IACzB;IAEA,IAAI;QACA,MAAMC,SAASF,OACTG,IAAAA,uBAAiB,EAACL,UAAU9B,WAC5B,MAAMoC,IAAAA,mBAAa,EAACN,UAAU9B;QAEpC,OAAOkC;IACX,EAAE,OAAOG,KAAU;QACf,IAAI,CAACA,IAAIC,OAAO,CAACC,QAAQ,CAAC,sBAAsB;YAC5C,MAAMF;QACV;IACJ;AACJ"}