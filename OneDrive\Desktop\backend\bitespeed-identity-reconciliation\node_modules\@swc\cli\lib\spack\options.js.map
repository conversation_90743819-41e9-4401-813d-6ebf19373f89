{"version": 3, "sources": ["../../src/spack/options.ts"], "sourcesContent": ["import { version as swcCoreVersion } from \"@swc/core\";\nimport { BundleOptions, compileBundleOptions } from \"@swc/core/spack\";\nimport commander from \"commander\";\nimport * as path from \"path\";\n\nconst pkg = require(\"../../package.json\");\n\nexport interface SpackCliOptions {\n    debug: boolean;\n}\n\nconst program = new commander.Command();\nprogram.option(\"--config [path]\", \"Path to a spack.config.js file to use.\");\n// TODO: allow using ts. See: https://github.com/swc-project/swc/issues/841\n\nprogram.option(\"--mode <development | production | none>\", \"Mode to use\");\nprogram.option(\"--target [browser | node]\", \"Target runtime environment\");\n\nprogram.option(\n    \"--context [path]\",\n    `The base directory (absolute path!) for resolving the 'entry'` +\n        ` option. If 'output.pathinfo' is set, the included pathinfo is shortened to this directory`,\n    \"The current directory\"\n);\n\nprogram.option(\"--entry [list]\", \"List of entries\", collect);\n\n// program.option('-W --watch', `Enter watch mode, which rebuilds on file change.`)\n\nprogram.option(\"--debug\", `Switch loaders to debug mode`);\n// program.option('--devtool', `Select a developer tool to enhance debugging.`)\n\n// -d           shortcut for --debug --devtool eval-cheap-module-source-map\n//              --output-pathinfo                                          [여부]\n// -p           shortcut for --optimize-minimize --define\n//              process.env.NODE_ENV=\"production\"                          [여부]\n\n// Module options:\n// --module-bind       Bind an extension to a loader                     [문자열]\n// --module-bind-post  Bind an extension to a post loader                [문자열]\n// --module-bind-pre   Bind an extension to a pre loader                 [문자열]\n\n// Output options:\nprogram.option(\n    \"-o --output\",\n    `The output path and file for compilation assets`\n);\nprogram.option(\"--output-path\", `The output directory as **absolute path**`);\n//   --output-filename             Specifies the name of each output file on disk.\n//                                 You must **not** specify an absolute path here!\n//                                 The `output.path` option determines the location\n//                                 on disk the files are written to, filename is\n//                                 used solely for naming the individual files.\n//                                                    [string] [default: [name].js]\n//   --output-chunk-filename       The filename of non-entry chunks as relative\n//                                 path inside the `output.path` directory.\n//        [string] [default: filename with [id] instead of [name] or [id] prefixed]\n//   --output-source-map-filename  The filename of the SourceMaps for the\n//                                 JavaScript files. They are inside the\n//                                 `output.path` directory.                [string]\n//   --output-public-path          The `publicPath` specifies the public URL\n//                                 address of the output files when referenced in a\n//                                 browser.                                [string]\n//   --output-jsonp-function       The JSONP function used by webpack for async\n//                                 loading of chunks.                      [string]\n//   --output-pathinfo             Include comments with information about the\n//                                 modules.                               [boolean]\n//   --output-library              Expose the exports of the entry point as library\n//                                                                          [array]\n//   --output-library-target       Type of library\n//          [string] [choices: \"var\", \"assign\", \"this\", \"window\", \"self\", \"global\",\n//       \"commonjs\", \"commonjs2\", \"commonjs-module\", \"amd\", \"umd\", \"umd2\", \"jsonp\"]\n\n// Advanced options:\n//   --records-input-path       Store compiler state to a json file.       [string]\n//   --records-output-path      Load compiler state from a json file.      [string]\n//   --records-path             Store/Load compiler state from/to a json file. This\n//                              will result in persistent ids of modules and\n//                              chunks. An absolute path is expected. `recordsPath`\n//                              is used for `recordsInputPath` and\n//                              `recordsOutputPath` if they left undefined.[string]\n//   --define                   Define any free var in the bundle          [string]\n//   --target                   Environment to build for                   [string]\n//   --cache                    Cache generated modules and chunks to improve\n//                              performance for multiple incremental builds.\n//                       [boolean] [default: It's enabled by default when watching]\n//   --watch-stdin, --stdin     Stop watching when stdin stream has ended [boolean]\n//   --watch-aggregate-timeout  Delay the rebuilt after the first change. Value is\n//                              a time in ms.                              [number]\n//   --watch-poll               Enable polling mode for watching           [string]\n//   --hot                      Enables Hot Module Replacement            [boolean]\n//   --prefetch                 Prefetch this request (Example: --prefetch\n//                              ./file.js)                                 [string]\n//   --provide                  Provide these modules as free vars in all modules\n//                              (Example: --provide jQuery=jquery)         [string]\n//   --labeled-modules          Enables labeled modules                   [boolean]\n//   --plugin                   Load this plugin                           [string]\n//   --bail                     Report the first error as a hard error instead of\n//                              tolerating it.            [boolean] [default: null]\n//   --profile                  Capture timing information for each module.\n//                                                        [boolean] [default: null]\n\n// Resolving options:\n//   --resolve-alias         Redirect module requests                      [string]\n//   --resolve-extensions    Redirect module requests                       [array]\n//   --resolve-loader-alias  Setup a loader alias for resolving            [string]\n\n// Optimizing options:\n//   --optimize-max-chunks      Try to keep the chunk count below a limit\n//   --optimize-min-chunk-size  Minimal size for the created chunk\n//   --optimize-minimize        Enable minimizing the output. Uses\n//                              optimization.minimizer.                   [boolean]\n\n// Stats options:\n//   --color, --colors               Force colors on the console\n//                                            [boolean] [default: (supports-color)]\n//   --no-color, --no-colors         Force no colors on the console       [boolean]\n//   --sort-modules-by               Sorts the modules list by property in module\n//                                                                         [string]\n//   --sort-chunks-by                Sorts the chunks list by property in chunk\n//                                                                         [string]\n//   --sort-assets-by                Sorts the assets list by property in asset\n//                                                                         [string]\n//   --hide-modules                  Hides info about modules             [boolean]\n//   --display-exclude               Exclude modules in the output         [string]\n//   --display-modules               Display even excluded modules in the output\n//                                                                        [boolean]\n//   --display-max-modules           Sets the maximum number of visible modules in\n//                                   output                                [number]\n//   --display-chunks                Display chunks in the output         [boolean]\n//   --display-entrypoints           Display entry points in the output   [boolean]\n//   --display-origins               Display origins of chunks in the output\n//                                                                        [boolean]\n//   --display-cached                Display also cached modules in the output\n//                                                                        [boolean]\n//   --display-cached-assets         Display also cached assets in the output\n//                                                                        [boolean]\n//   --display-reasons               Display reasons about module inclusion in the\n//                                   output                               [boolean]\n//   --display-depth                 Display distance from entry point for each\n//                                   module                               [boolean]\n//   --display-used-exports          Display information about used exports in\n//                                   modules (Tree Shaking)               [boolean]\n//   --display-provided-exports      Display information about exports provided\n//                                   from modules                         [boolean]\n//   --display-optimization-bailout  Display information about why optimization\n//                                   bailed out for modules               [boolean]\n//   --display-error-details         Display details about errors         [boolean]\n//   --display                       Select display preset\n//               [string] [choices: \"\", \"verbose\", \"detailed\", \"normal\", \"minimal\",\n//                                                           \"errors-only\", \"none\"]\n//   --verbose                       Show more details                    [boolean]\n//   --info-verbosity                Controls the output of lifecycle messaging\n//                                   e.g. Started watching files...\n//                  [string] [choices: \"none\", \"info\", \"verbose\"] [default: \"info\"]\n//   --build-delimiter               Display custom text after build output[string]\n\n// Options:\n//   --silent       Prevent output from being displayed in stdout         [boolean]\n//   --json, -j     Prints the result as JSON.                            [boolean]\n\nprogram.version(\n    `@swc/cli: ${pkg.version}\n@swc/core: ${swcCoreVersion}`\n);\n\nexport default async function parseSpackArgs(args: string[]): Promise<{\n    cliOptions: SpackCliOptions;\n    spackOptions: BundleOptions;\n}> {\n    //\n    const cmd = program.parse(args);\n    const opts = cmd.opts();\n\n    const cliOptions: SpackCliOptions = {\n        // watch: !!opts.watch,\n        debug: !!opts.debug,\n    };\n\n    const configOpts: BundleOptions = (await compileBundleOptions(\n        opts.config ?? path.resolve(\"spack.config.js\")\n    )) as any;\n    if (opts.entry) {\n        configOpts.entry = opts.entry;\n    }\n    if (opts.mode) {\n        configOpts.mode = opts.mode;\n    }\n    if (opts.target) {\n        configOpts.target = opts.target;\n    }\n    if (!configOpts.output) {\n        configOpts.output = {} as any;\n    }\n    if (!configOpts.output.path) {\n        configOpts.output.path = opts.outputPath ?? \"[name].js\";\n    }\n    if (!configOpts.output.name) {\n        configOpts.output.name = opts.output ?? \"[name].js\";\n    }\n    // if (!configOpts.output.name) {\n    //     configOpts.output.path = opts.outputPath;\n    // }\n\n    return {\n        cliOptions,\n        spackOptions: {\n            ...configOpts,\n        },\n    };\n}\n\nfunction collect(value: any, previousValue: any): Array<string> {\n    // If the user passed the option with no value, like \"babel file.js --presets\", do nothing.\n    if (typeof value !== \"string\") return previousValue;\n\n    const values = value.split(\",\");\n\n    return previousValue ? previousValue.concat(values) : values;\n}\n"], "names": ["parseSpackArgs", "pkg", "require", "program", "commander", "Command", "option", "collect", "version", "swcCoreVersion", "args", "cmd", "parse", "opts", "cliOptions", "debug", "configOpts", "compileBundleOptions", "config", "path", "resolve", "entry", "mode", "target", "output", "outputPath", "name", "spackOptions", "value", "previousValue", "values", "split", "concat"], "mappings": ";;;;+BAsKA;;;eAA8BA;;;sBAtKY;uBACU;kEAC9B;8DACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEtB,MAAMC,MAAMC,QAAQ;AAMpB,MAAMC,UAAU,IAAIC,kBAAS,CAACC,OAAO;AACrCF,QAAQG,MAAM,CAAC,mBAAmB;AAClC,2EAA2E;AAE3EH,QAAQG,MAAM,CAAC,4CAA4C;AAC3DH,QAAQG,MAAM,CAAC,6BAA6B;AAE5CH,QAAQG,MAAM,CACV,oBACA,CAAC,6DAA6D,CAAC,GAC3D,CAAC,0FAA0F,CAAC,EAChG;AAGJH,QAAQG,MAAM,CAAC,kBAAkB,mBAAmBC;AAEpD,mFAAmF;AAEnFJ,QAAQG,MAAM,CAAC,WAAW,CAAC,4BAA4B,CAAC;AACxD,+EAA+E;AAE/E,2EAA2E;AAC3E,+EAA+E;AAC/E,yDAAyD;AACzD,+EAA+E;AAE/E,kBAAkB;AAClB,8EAA8E;AAC9E,8EAA8E;AAC9E,8EAA8E;AAE9E,kBAAkB;AAClBH,QAAQG,MAAM,CACV,eACA,CAAC,+CAA+C,CAAC;AAErDH,QAAQG,MAAM,CAAC,iBAAiB,CAAC,yCAAyC,CAAC;AAC3E,kFAAkF;AAClF,kFAAkF;AAClF,mFAAmF;AACnF,gFAAgF;AAChF,+EAA+E;AAC/E,mFAAmF;AACnF,+EAA+E;AAC/E,2EAA2E;AAC3E,mFAAmF;AACnF,yEAAyE;AACzE,wEAAwE;AACxE,mFAAmF;AACnF,4EAA4E;AAC5E,mFAAmF;AACnF,mFAAmF;AACnF,+EAA+E;AAC/E,mFAAmF;AACnF,8EAA8E;AAC9E,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,kDAAkD;AAClD,mFAAmF;AACnF,mFAAmF;AAEnF,oBAAoB;AACpB,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,4EAA4E;AAC5E,mFAAmF;AACnF,kEAAkE;AAClE,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,6EAA6E;AAC7E,4EAA4E;AAC5E,mFAAmF;AACnF,mFAAmF;AACnF,kFAAkF;AAClF,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,0EAA0E;AAC1E,mFAAmF;AACnF,iFAAiF;AACjF,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,iFAAiF;AACjF,mFAAmF;AACnF,2EAA2E;AAC3E,mFAAmF;AAEnF,qBAAqB;AACrB,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AAEnF,sBAAsB;AACtB,yEAAyE;AACzE,kEAAkE;AAClE,kEAAkE;AAClE,mFAAmF;AAEnF,iBAAiB;AACjB,gEAAgE;AAChE,mFAAmF;AACnF,mFAAmF;AACnF,iFAAiF;AACjF,mFAAmF;AACnF,+EAA+E;AAC/E,mFAAmF;AACnF,+EAA+E;AAC/E,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,gFAAgF;AAChF,mFAAmF;AACnF,kFAAkF;AAClF,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,4EAA4E;AAC5E,mFAAmF;AACnF,8EAA8E;AAC9E,mFAAmF;AACnF,6EAA6E;AAC7E,mFAAmF;AACnF,kFAAkF;AAClF,mFAAmF;AACnF,+EAA+E;AAC/E,mFAAmF;AACnF,8EAA8E;AAC9E,mFAAmF;AACnF,+EAA+E;AAC/E,mFAAmF;AACnF,+EAA+E;AAC/E,mFAAmF;AACnF,mFAAmF;AACnF,0DAA0D;AAC1D,mFAAmF;AACnF,mFAAmF;AACnF,mFAAmF;AACnF,+EAA+E;AAC/E,mEAAmE;AACnE,mFAAmF;AACnF,mFAAmF;AAEnF,WAAW;AACX,mFAAmF;AACnF,mFAAmF;AAEnFH,QAAQK,OAAO,CACX,CAAC,UAAU,EAAEP,IAAIO,OAAO,CAAC;WAClB,EAAEC,aAAc,CAAC,CAAC;AAGd,eAAeT,eAAeU,IAAc;IAIvD,EAAE;IACF,MAAMC,MAAMR,QAAQS,KAAK,CAACF;IAC1B,MAAMG,OAAOF,IAAIE,IAAI;IAErB,MAAMC,aAA8B;QAChC,uBAAuB;QACvBC,OAAO,CAAC,CAACF,KAAKE,KAAK;IACvB;QAGIF;IADJ,MAAMG,aAA6B,MAAMC,IAAAA,2BAAoB,EACzDJ,CAAAA,eAAAA,KAAKK,MAAM,cAAXL,0BAAAA,eAAeM,MAAKC,OAAO,CAAC;IAEhC,IAAIP,KAAKQ,KAAK,EAAE;QACZL,WAAWK,KAAK,GAAGR,KAAKQ,KAAK;IACjC;IACA,IAAIR,KAAKS,IAAI,EAAE;QACXN,WAAWM,IAAI,GAAGT,KAAKS,IAAI;IAC/B;IACA,IAAIT,KAAKU,MAAM,EAAE;QACbP,WAAWO,MAAM,GAAGV,KAAKU,MAAM;IACnC;IACA,IAAI,CAACP,WAAWQ,MAAM,EAAE;QACpBR,WAAWQ,MAAM,GAAG,CAAC;IACzB;IACA,IAAI,CAACR,WAAWQ,MAAM,CAACL,IAAI,EAAE;YACAN;QAAzBG,WAAWQ,MAAM,CAACL,IAAI,GAAGN,CAAAA,mBAAAA,KAAKY,UAAU,cAAfZ,8BAAAA,mBAAmB;IAChD;IACA,IAAI,CAACG,WAAWQ,MAAM,CAACE,IAAI,EAAE;YACAb;QAAzBG,WAAWQ,MAAM,CAACE,IAAI,GAAGb,CAAAA,eAAAA,KAAKW,MAAM,cAAXX,0BAAAA,eAAe;IAC5C;IACA,iCAAiC;IACjC,gDAAgD;IAChD,IAAI;IAEJ,OAAO;QACHC;QACAa,cAAc;YACV,GAAGX,UAAU;QACjB;IACJ;AACJ;AAEA,SAAST,QAAQqB,KAAU,EAAEC,aAAkB;IAC3C,2FAA2F;IAC3F,IAAI,OAAOD,UAAU,UAAU,OAAOC;IAEtC,MAAMC,SAASF,MAAMG,KAAK,CAAC;IAE3B,OAAOF,gBAAgBA,cAAcG,MAAM,CAACF,UAAUA;AAC1D"}