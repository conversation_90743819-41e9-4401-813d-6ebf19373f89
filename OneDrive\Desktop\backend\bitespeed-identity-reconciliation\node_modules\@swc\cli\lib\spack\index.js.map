{"version": 3, "sources": ["../../src/spack/index.ts"], "sourcesContent": ["import { bundle } from \"@swc/core\";\nimport { mkdir, writeFile } from \"fs\";\nimport { basename, dirname, extname, join, relative } from \"path\";\nimport { promisify } from \"util\";\n\nimport parseSpackArgs from \"./options\";\n\nconst write = promisify(writeFile);\nconst makeDir = promisify(mkdir);\n\n(async () => {\n    const { spackOptions } = await parseSpackArgs(process.argv);\n\n    function isUserDefinedEntry(name: string) {\n        if (typeof spackOptions.entry === \"string\") {\n            return spackOptions.entry === name;\n        }\n        if (Array.isArray(spackOptions.entry)) {\n            for (const e of spackOptions.entry) {\n                if (e === name) {\n                    return true;\n                }\n            }\n            return false;\n        }\n\n        return name in spackOptions.entry;\n    }\n\n    async function build() {\n        const bundleStart = process.hrtime();\n        const output = await bundle(spackOptions);\n        const bundleEnd = process.hrtime(bundleStart);\n        console.info(\n            `Bundling done: ${bundleEnd[0]}s ${bundleEnd[1] / 1000000}ms`\n        );\n\n        const emitStart = process.hrtime();\n        if (spackOptions.output?.path) {\n            await Object.keys(output).map(async name => {\n                let fullPath = \"\";\n                if (isUserDefinedEntry(name)) {\n                    fullPath = join(\n                        spackOptions.output.path,\n                        spackOptions.output.name.replace(\"[name]\", name)\n                    );\n                } else {\n                    const ext = extname(name);\n                    const base = basename(name, ext);\n                    const filename = relative(process.cwd(), name);\n                    fullPath = join(\n                        spackOptions.output.path,\n                        dirname(filename),\n                        `${base}.js`\n                    );\n                }\n\n                await makeDir(dirname(fullPath), { recursive: true });\n                await write(fullPath, output[name].code, \"utf-8\");\n                if (output[name].map) {\n                    await write(`${fullPath}.map`, output[name].map!, \"utf-8\");\n                }\n            });\n        } else {\n            throw new Error(\"Cannot print to stdout: not implemented yet\");\n        }\n        const emitEnd = process.hrtime(emitStart);\n        console.info(`Done: ${emitEnd[0]}s ${emitEnd[1] / 1000000}ms`);\n    }\n\n    // if (cliOptions.watch) {\n    //     throw new Error('watch is not implemented yet')\n    // }\n\n    await build();\n})();\n"], "names": ["write", "promisify", "writeFile", "makeDir", "mkdir", "spackOptions", "parseSpackArgs", "process", "argv", "isUserDefinedEntry", "name", "entry", "Array", "isArray", "e", "build", "bundleStart", "hrtime", "output", "bundle", "bundleEnd", "console", "info", "emitStart", "path", "Object", "keys", "map", "fullPath", "join", "replace", "ext", "extname", "base", "basename", "filename", "relative", "cwd", "dirname", "recursive", "code", "Error", "emitEnd"], "mappings": ";;;;sBAAuB;oBACU;sBAC0B;sBACjC;gEAEC;;;;;;AAE3B,MAAMA,QAAQC,IAAAA,eAAS,EAACC,aAAS;AACjC,MAAMC,UAAUF,IAAAA,eAAS,EAACG,SAAK;AAE9B,CAAA;IACG,MAAM,EAAEC,YAAY,EAAE,GAAG,MAAMC,IAAAA,gBAAc,EAACC,QAAQC,IAAI;IAE1D,SAASC,mBAAmBC,IAAY;QACpC,IAAI,OAAOL,aAAaM,KAAK,KAAK,UAAU;YACxC,OAAON,aAAaM,KAAK,KAAKD;QAClC;QACA,IAAIE,MAAMC,OAAO,CAACR,aAAaM,KAAK,GAAG;YACnC,KAAK,MAAMG,KAAKT,aAAaM,KAAK,CAAE;gBAChC,IAAIG,MAAMJ,MAAM;oBACZ,OAAO;gBACX;YACJ;YACA,OAAO;QACX;QAEA,OAAOA,QAAQL,aAAaM,KAAK;IACrC;IAEA,eAAeI;YASPV;QARJ,MAAMW,cAAcT,QAAQU,MAAM;QAClC,MAAMC,SAAS,MAAMC,IAAAA,YAAM,EAACd;QAC5B,MAAMe,YAAYb,QAAQU,MAAM,CAACD;QACjCK,QAAQC,IAAI,CACR,CAAC,eAAe,EAAEF,SAAS,CAAC,EAAE,CAAC,EAAE,EAAEA,SAAS,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC;QAGjE,MAAMG,YAAYhB,QAAQU,MAAM;QAChC,KAAIZ,uBAAAA,aAAaa,MAAM,cAAnBb,2CAAAA,qBAAqBmB,IAAI,EAAE;YAC3B,MAAMC,OAAOC,IAAI,CAACR,QAAQS,GAAG,CAAC,OAAMjB;gBAChC,IAAIkB,WAAW;gBACf,IAAInB,mBAAmBC,OAAO;oBAC1BkB,WAAWC,IAAAA,UAAI,EACXxB,aAAaa,MAAM,CAACM,IAAI,EACxBnB,aAAaa,MAAM,CAACR,IAAI,CAACoB,OAAO,CAAC,UAAUpB;gBAEnD,OAAO;oBACH,MAAMqB,MAAMC,IAAAA,aAAO,EAACtB;oBACpB,MAAMuB,OAAOC,IAAAA,cAAQ,EAACxB,MAAMqB;oBAC5B,MAAMI,WAAWC,IAAAA,cAAQ,EAAC7B,QAAQ8B,GAAG,IAAI3B;oBACzCkB,WAAWC,IAAAA,UAAI,EACXxB,aAAaa,MAAM,CAACM,IAAI,EACxBc,IAAAA,aAAO,EAACH,WACR,CAAC,EAAEF,KAAK,GAAG,CAAC;gBAEpB;gBAEA,MAAM9B,QAAQmC,IAAAA,aAAO,EAACV,WAAW;oBAAEW,WAAW;gBAAK;gBACnD,MAAMvC,MAAM4B,UAAUV,MAAM,CAACR,KAAK,CAAC8B,IAAI,EAAE;gBACzC,IAAItB,MAAM,CAACR,KAAK,CAACiB,GAAG,EAAE;oBAClB,MAAM3B,MAAM,CAAC,EAAE4B,SAAS,IAAI,CAAC,EAAEV,MAAM,CAACR,KAAK,CAACiB,GAAG,EAAG;gBACtD;YACJ;QACJ,OAAO;YACH,MAAM,IAAIc,MAAM;QACpB;QACA,MAAMC,UAAUnC,QAAQU,MAAM,CAACM;QAC/BF,QAAQC,IAAI,CAAC,CAAC,MAAM,EAAEoB,OAAO,CAAC,EAAE,CAAC,EAAE,EAAEA,OAAO,CAAC,EAAE,GAAG,QAAQ,EAAE,CAAC;IACjE;IAEA,0BAA0B;IAC1B,sDAAsD;IACtD,IAAI;IAEJ,MAAM3B;AACV,CAAA"}