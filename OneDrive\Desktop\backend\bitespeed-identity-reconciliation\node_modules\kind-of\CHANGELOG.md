# Release history

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](http://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](http://semver.org/spec/v2.0.0.html).

<details>
  <summary><strong>Guiding Principles</strong></summary>

- Changelogs are for humans, not machines.
- There should be an entry for every single version.
- The same types of changes should be grouped.
- Versions and sections should be linkable.
- The latest version comes first.
- The release date of each versions is displayed.
- Mention whether you follow Semantic Versioning.

</details>

<details>
  <summary><strong>Types of changes</strong></summary>

Changelog entries are classified using the following labels _(from [keep-a-changelog](http://keepachangelog.com/)_):

- `Added` for new features.
- `Changed` for changes in existing functionality.
- `Deprecated` for soon-to-be removed features.
- `Removed` for now removed features.
- `Fixed` for any bug fixes.
- `Security` in case of vulnerabilities.

</details>

## [6.0.3] - 2020-01-16

- Merge pull request #31 for issue #30

## [6.0.0] - 2017-10-13

- refactor code to be more performant
- refactor benchmarks

## [5.1.0] - 2017-10-13

**Added**

- Merge pull request #15 from aretecode/patch-1
- adds support and tests for string & array iterators

**Changed**

- updates benchmarks

## [5.0.2] - 2017-08-02

- Merge pull request #14 from struct78/master
- Added `undefined` check

## [5.0.0] - 2017-06-21

- Merge pull request #12 from aretecode/iterator
- Set Iterator + Map Iterator
- streamline `isbuffer`, minor edits

## [4.0.0] - 2017-05-19

- Merge pull request #8 from tunnckoCore/master
- update deps

## [3.2.2] - 2017-05-16

- fix version

## [3.2.1] - 2017-05-16

- add browserify

## [3.2.0] - 2017-04-25

- Merge pull request #10 from ksheedlo/unrequire-buffer
- add `promise` support and tests
- Remove unnecessary `Buffer` check

## [3.1.0] - 2016-12-07

- Merge pull request #7 from laggingreflex/err
- add support for `error` and tests
- run update

## [3.0.4] - 2016-07-29

- move tests
- run update

## [3.0.3] - 2016-05-03

- fix prepublish script
- remove unused dep

## [3.0.0] - 2015-11-17

- add typed array support
- Merge pull request #5 from miguelmota/typed-arrays
- adds new tests

## [2.0.1] - 2015-08-21

- use `is-buffer` module

## [2.0.0] - 2015-05-31

- Create fallback for `Array.isArray` if used as a browser package
- Merge pull request #2 from dtothefp/patch-1
- Merge pull request #3 from pdehaan/patch-1
- Merge branch 'master' of https://github.com/chorks/kind-of into chorks-master
- optimizations, mostly date and regex

## [1.1.0] - 2015-02-09

- adds `buffer` support
- adds tests for `buffer`

## [1.0.0] - 2015-01-19

- update benchmarks
- optimizations based on benchmarks

## [0.1.2] - 2014-10-26

- return `typeof` value if it's not an object. very slight speed improvement
- use `.slice`
- adds benchmarks

## [0.1.0] - 2014-9-26

- first commit

[6.0.0]: https://github.com/jonschlinkert/kind-of/compare/5.1.0...6.0.0
[5.1.0]: https://github.com/jonschlinkert/kind-of/compare/5.0.2...5.1.0
[5.0.2]: https://github.com/jonschlinkert/kind-of/compare/5.0.1...5.0.2
[5.0.1]: https://github.com/jonschlinkert/kind-of/compare/5.0.0...5.0.1
[5.0.0]: https://github.com/jonschlinkert/kind-of/compare/4.0.0...5.0.0
[4.0.0]: https://github.com/jonschlinkert/kind-of/compare/3.2.2...4.0.0
[3.2.2]: https://github.com/jonschlinkert/kind-of/compare/3.2.1...3.2.2
[3.2.1]: https://github.com/jonschlinkert/kind-of/compare/3.2.0...3.2.1
[3.2.0]: https://github.com/jonschlinkert/kind-of/compare/3.1.0...3.2.0
[3.1.0]: https://github.com/jonschlinkert/kind-of/compare/3.0.4...3.1.0
[3.0.4]: https://github.com/jonschlinkert/kind-of/compare/3.0.3...3.0.4
[3.0.3]: https://github.com/jonschlinkert/kind-of/compare/3.0.0...3.0.3
[3.0.0]: https://github.com/jonschlinkert/kind-of/compare/2.0.1...3.0.0
[2.0.1]: https://github.com/jonschlinkert/kind-of/compare/2.0.0...2.0.1
[2.0.0]: https://github.com/jonschlinkert/kind-of/compare/1.1.0...2.0.0
[1.1.0]: https://github.com/jonschlinkert/kind-of/compare/1.0.0...1.1.0
[1.0.0]: https://github.com/jonschlinkert/kind-of/compare/0.1.2...1.0.0
[0.1.2]: https://github.com/jonschlinkert/kind-of/compare/0.1.0...0.1.2
[0.1.0]: https://github.com/jonschlinkert/kind-of/commit/2fae09b0b19b1aadb558e9be39f0c3ef6034eb87

[Unreleased]: https://github.com/jonschlinkert/kind-of/compare/0.1.2...HEAD
[keep-a-changelog]: https://github.com/olivierlacan/keep-a-changelog