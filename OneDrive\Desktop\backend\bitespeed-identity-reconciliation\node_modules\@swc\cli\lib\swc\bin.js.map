{"version": 3, "sources": ["../../src/swc/bin.ts"], "sourcesContent": ["import dirCommand from \"./dir\";\nimport fileCommand from \"./file\";\nimport parseArgs, { initProgram } from \"./options\";\n\ninitProgram();\nconst opts = parseArgs(process.argv);\nconst fn = opts.cliOptions.outDir ? dirCommand : fileCommand;\n\nprocess.on(\"uncaughtException\", function (err) {\n    console.error(err);\n    process.exit(1);\n});\n\nfn(opts).catch((err: Error) => {\n    console.error(err);\n    process.exit(1);\n});\n"], "names": ["initProgram", "opts", "parseArgs", "process", "argv", "fn", "cliOptions", "outDir", "dir<PERSON><PERSON><PERSON>", "fileCommand", "on", "err", "console", "error", "exit", "catch"], "mappings": ";;;;4DAAuB;6DACC;iEACe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvCA,IAAAA,oBAAW;AACX,MAAMC,OAAOC,IAAAA,gBAAS,EAACC,QAAQC,IAAI;AACnC,MAAMC,KAAKJ,KAAKK,UAAU,CAACC,MAAM,GAAGC,YAAU,GAAGC,aAAW;AAE5DN,QAAQO,EAAE,CAAC,qBAAqB,SAAUC,GAAG;IACzCC,QAAQC,KAAK,CAACF;IACdR,QAAQW,IAAI,CAAC;AACjB;AAEAT,GAAGJ,MAAMc,KAAK,CAAC,CAACJ;IACZC,QAAQC,KAAK,CAACF;IACdR,QAAQW,IAAI,CAAC;AACjB"}