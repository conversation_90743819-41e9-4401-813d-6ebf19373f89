import type { ReadableStream as NodeReadableStream } from 'node:stream/web';
export { EndOfStreamError } from './EndOfStreamError.js';
import { AbstractStreamReader } from "./AbstractStreamReader.js";
export type AnyWebByteStream = NodeReadableStream<Uint8Array> | ReadableStream<Uint8Array>;
/**
 * Read from a WebStream
 * Reference: https://nodejs.org/api/webstreams.html#class-readablestreambyobreader
 */
export declare class WebStreamReader extends AbstractStreamReader {
    private reader;
    constructor(stream: AnyWebByteStream);
    protected readFromStream(buffer: Uint8Array, offset: number, length: number): Promise<number>;
    abort(): Promise<void>;
    close(): Promise<void>;
}
