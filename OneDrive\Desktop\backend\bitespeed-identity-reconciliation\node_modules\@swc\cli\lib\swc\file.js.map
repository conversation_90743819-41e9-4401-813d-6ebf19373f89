{"version": 3, "sources": ["../../src/swc/file.ts"], "sourcesContent": ["import swc from \"@swc/core\";\nimport path from \"path\";\nimport slash from \"slash\";\nimport { SourceMapConsumer, SourceMapGenerator } from \"source-map\";\n\nimport { CliOptions } from \"./options\";\nimport { globSources, isCompilableExtension, watchSources } from \"./sources\";\nimport * as util from \"./util\";\n\nexport default async function ({\n    cliOptions,\n    swcOptions,\n}: {\n    cliOptions: CliOptions;\n    swcOptions: swc.Options;\n}) {\n    async function concatResults(\n        file: string,\n        ...results: swc.Output[]\n    ): Promise<swc.Output> {\n        let added = false;\n        const map = new SourceMapGenerator({\n            file,\n            sourceRoot: swcOptions.sourceRoot,\n        });\n\n        let code = \"\";\n        let offset = 0;\n\n        for (const result of results) {\n            code += result.code + \"\\n\";\n\n            if (result.map) {\n                added = true;\n\n                const consumer = await new SourceMapConsumer(result.map);\n                const sources = new Set<string>();\n\n                consumer.eachMapping(mapping => {\n                    sources.add(mapping.source);\n                    map.addMapping({\n                        generated: {\n                            line: mapping.generatedLine + offset,\n                            column: mapping.generatedColumn,\n                        },\n                        original: {\n                            line: mapping.originalLine,\n                            column: mapping.originalColumn,\n                        },\n                        source: mapping.source,\n                    });\n                });\n\n                sources.forEach(source => {\n                    const content = consumer.sourceContentFor(source, true);\n                    if (content !== null) {\n                        map.setSourceContent(source, content);\n                    }\n                });\n            }\n            offset = code.split(\"\\n\").length - 1;\n        }\n\n        if (!added) {\n            return { code };\n        }\n\n        return {\n            code,\n            map: JSON.stringify(map),\n        };\n    }\n\n    async function output(results: Iterable<swc.Output>) {\n        const file =\n            cliOptions.sourceMapTarget ||\n            path.basename(cliOptions.outFile || \"stdout\");\n\n        const result = await concatResults(file, ...results);\n\n        if (cliOptions.outFile) {\n            util.outputFile(result, cliOptions.outFile, swcOptions.sourceMaps);\n        } else {\n            process.stdout.write(result.code + \"\\n\");\n            if (result.map) {\n                const map = `//#sourceMappingURL=data:application/json;charset=utf-8;base64,${Buffer.from(\n                    JSON.stringify(result.map),\n                    \"utf8\"\n                ).toString(\"base64\")}`;\n                process.stdout.write(map);\n            }\n        }\n    }\n\n    async function handle(filename: string) {\n        const sourceFileName = slash(\n            cliOptions.outFile\n                ? path.relative(path.dirname(cliOptions.outFile), filename)\n                : filename\n        );\n        return await util.compile(\n            filename,\n            {\n                ...swcOptions,\n                sourceFileName,\n            },\n            cliOptions.sync,\n            cliOptions.outFile\n        );\n    }\n\n    async function getProgram(\n        previousResults: Map<string, swc.Output | Error> = new Map()\n    ) {\n        const results: typeof previousResults = new Map();\n\n        for (const filename of await globSources(\n            cliOptions.filenames,\n            cliOptions.only,\n            cliOptions.ignore,\n            cliOptions.includeDotfiles\n        )) {\n            if (isCompilableExtension(filename, cliOptions.extensions)) {\n                results.set(filename, previousResults.get(filename)!);\n            }\n        }\n        return results;\n    }\n\n    async function files() {\n        let results = await getProgram();\n        for (const filename of results.keys()) {\n            try {\n                const result = await handle(filename);\n                if (result) {\n                    results.set(filename, result);\n                } else {\n                    results.delete(filename);\n                }\n            } catch (err: any) {\n                console.error(err.message);\n                results.set(filename, err);\n            }\n        }\n\n        if (cliOptions.watch) {\n            const watcher = await watchSources(\n                cliOptions.filenames,\n                cliOptions.includeDotfiles\n            );\n            watcher.on(\"ready\", () => {\n                Promise.resolve()\n                    .then(async () => {\n                        util.assertCompilationResult(results, cliOptions.quiet);\n                        await output(results.values());\n                        if (!cliOptions.quiet) {\n                            console.info(\"Watching for file changes.\");\n                        }\n                    })\n                    .catch(err => {\n                        console.error(err.message);\n                    });\n            });\n            watcher.on(\"add\", async filename => {\n                if (isCompilableExtension(filename, cliOptions.extensions)) {\n                    // ensure consistent insertion order when files are added\n                    results = await getProgram(results);\n                }\n            });\n            watcher.on(\"unlink\", filename => {\n                results.delete(filename);\n            });\n            for (const type of [\"add\", \"change\"]) {\n                watcher.on(type, filename => {\n                    if (\n                        !isCompilableExtension(filename, cliOptions.extensions)\n                    ) {\n                        return;\n                    }\n\n                    const start = process.hrtime();\n\n                    handle(filename)\n                        .then(async result => {\n                            if (!result) {\n                                results.delete(filename);\n                                return;\n                            }\n                            results.set(filename, result);\n                            util.assertCompilationResult(results, true);\n                            await output(results.values());\n                            if (!cliOptions.quiet) {\n                                const [seconds, nanoseconds] =\n                                    process.hrtime(start);\n                                const ms = seconds * 1000 + nanoseconds * 1e-6;\n                                const name = path.basename(cliOptions.outFile);\n                                console.log(\n                                    `Compiled ${name} in ${ms.toFixed(2)}ms`\n                                );\n                            }\n                        })\n                        .catch(err => {\n                            console.error(err.message);\n                        });\n                });\n            }\n        } else {\n            util.assertCompilationResult(results, cliOptions.quiet);\n            await output(results.values());\n        }\n    }\n\n    async function stdin() {\n        let code = \"\";\n        process.stdin.setEncoding(\"utf8\");\n        for await (const chunk of process.stdin) {\n            code += chunk;\n        }\n        const res = await util.transform(\n            cliOptions.filename,\n            code,\n            {\n                ...swcOptions,\n                sourceFileName: \"stdin\",\n            },\n            cliOptions.sync,\n            undefined\n        );\n\n        output([res]);\n    }\n\n    if (cliOptions.filenames.length) {\n        await files();\n    } else {\n        await stdin();\n    }\n}\n"], "names": ["cliOptions", "swcOptions", "concatResults", "file", "results", "added", "map", "SourceMapGenerator", "sourceRoot", "code", "offset", "result", "consumer", "SourceMapConsumer", "sources", "Set", "eachMapping", "mapping", "add", "source", "addMapping", "generated", "line", "generatedLine", "column", "generatedColumn", "original", "originalLine", "originalColumn", "for<PERSON>ach", "content", "sourceContentFor", "setSourceContent", "split", "length", "JSON", "stringify", "output", "sourceMapTarget", "path", "basename", "outFile", "util", "outputFile", "sourceMaps", "process", "stdout", "write", "<PERSON><PERSON><PERSON>", "from", "toString", "handle", "filename", "sourceFileName", "slash", "relative", "dirname", "compile", "sync", "getProgram", "previousResults", "Map", "globSources", "filenames", "only", "ignore", "includeDotfiles", "isCompilableExtension", "extensions", "set", "get", "files", "keys", "delete", "err", "console", "error", "message", "watch", "watcher", "watchSources", "on", "Promise", "resolve", "then", "assertCompilationResult", "quiet", "values", "info", "catch", "type", "start", "hrtime", "seconds", "nanoseconds", "ms", "name", "log", "toFixed", "stdin", "setEncoding", "chunk", "res", "transform", "undefined"], "mappings": ";;;;+BASA;;;eAAA;;;6DARiB;8DACC;2BACoC;yBAGW;8DAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEP,eAAf,SAA+B,EAC3BA,UAAU,EACVC,UAAU,EAIb;IACG,eAAeC,cACXC,IAAY,EACZ,GAAGC,OAAqB;QAExB,IAAIC,QAAQ;QACZ,MAAMC,MAAM,IAAIC,6BAAkB,CAAC;YAC/BJ;YACAK,YAAYP,WAAWO,UAAU;QACrC;QAEA,IAAIC,OAAO;QACX,IAAIC,SAAS;QAEb,KAAK,MAAMC,UAAUP,QAAS;YAC1BK,QAAQE,OAAOF,IAAI,GAAG;YAEtB,IAAIE,OAAOL,GAAG,EAAE;gBACZD,QAAQ;gBAER,MAAMO,WAAW,MAAM,IAAIC,4BAAiB,CAACF,OAAOL,GAAG;gBACvD,MAAMQ,UAAU,IAAIC;gBAEpBH,SAASI,WAAW,CAACC,CAAAA;oBACjBH,QAAQI,GAAG,CAACD,QAAQE,MAAM;oBAC1Bb,IAAIc,UAAU,CAAC;wBACXC,WAAW;4BACPC,MAAML,QAAQM,aAAa,GAAGb;4BAC9Bc,QAAQP,QAAQQ,eAAe;wBACnC;wBACAC,UAAU;4BACNJ,MAAML,QAAQU,YAAY;4BAC1BH,QAAQP,QAAQW,cAAc;wBAClC;wBACAT,QAAQF,QAAQE,MAAM;oBAC1B;gBACJ;gBAEAL,QAAQe,OAAO,CAACV,CAAAA;oBACZ,MAAMW,UAAUlB,SAASmB,gBAAgB,CAACZ,QAAQ;oBAClD,IAAIW,YAAY,MAAM;wBAClBxB,IAAI0B,gBAAgB,CAACb,QAAQW;oBACjC;gBACJ;YACJ;YACApB,SAASD,KAAKwB,KAAK,CAAC,MAAMC,MAAM,GAAG;QACvC;QAEA,IAAI,CAAC7B,OAAO;YACR,OAAO;gBAAEI;YAAK;QAClB;QAEA,OAAO;YACHA;YACAH,KAAK6B,KAAKC,SAAS,CAAC9B;QACxB;IACJ;IAEA,eAAe+B,OAAOjC,OAA6B;QAC/C,MAAMD,OACFH,WAAWsC,eAAe,IAC1BC,aAAI,CAACC,QAAQ,CAACxC,WAAWyC,OAAO,IAAI;QAExC,MAAM9B,SAAS,MAAMT,cAAcC,SAASC;QAE5C,IAAIJ,WAAWyC,OAAO,EAAE;YACpBC,MAAKC,UAAU,CAAChC,QAAQX,WAAWyC,OAAO,EAAExC,WAAW2C,UAAU;QACrE,OAAO;YACHC,QAAQC,MAAM,CAACC,KAAK,CAACpC,OAAOF,IAAI,GAAG;YACnC,IAAIE,OAAOL,GAAG,EAAE;gBACZ,MAAMA,MAAM,CAAC,+DAA+D,EAAE0C,OAAOC,IAAI,CACrFd,KAAKC,SAAS,CAACzB,OAAOL,GAAG,GACzB,QACF4C,QAAQ,CAAC,UAAU,CAAC;gBACtBL,QAAQC,MAAM,CAACC,KAAK,CAACzC;YACzB;QACJ;IACJ;IAEA,eAAe6C,OAAOC,QAAgB;QAClC,MAAMC,iBAAiBC,IAAAA,cAAK,EACxBtD,WAAWyC,OAAO,GACZF,aAAI,CAACgB,QAAQ,CAAChB,aAAI,CAACiB,OAAO,CAACxD,WAAWyC,OAAO,GAAGW,YAChDA;QAEV,OAAO,MAAMV,MAAKe,OAAO,CACrBL,UACA;YACI,GAAGnD,UAAU;YACboD;QACJ,GACArD,WAAW0D,IAAI,EACf1D,WAAWyC,OAAO;IAE1B;IAEA,eAAekB,WACXC,kBAAmD,IAAIC,KAAK;QAE5D,MAAMzD,UAAkC,IAAIyD;QAE5C,KAAK,MAAMT,YAAY,CAAA,MAAMU,IAAAA,oBAAW,EACpC9D,WAAW+D,SAAS,EACpB/D,WAAWgE,IAAI,EACfhE,WAAWiE,MAAM,EACjBjE,WAAWkE,eAAe,CAC9B,EAAG;YACC,IAAIC,IAAAA,8BAAqB,EAACf,UAAUpD,WAAWoE,UAAU,GAAG;gBACxDhE,QAAQiE,GAAG,CAACjB,UAAUQ,gBAAgBU,GAAG,CAAClB;YAC9C;QACJ;QACA,OAAOhD;IACX;IAEA,eAAemE;QACX,IAAInE,UAAU,MAAMuD;QACpB,KAAK,MAAMP,YAAYhD,QAAQoE,IAAI,GAAI;YACnC,IAAI;gBACA,MAAM7D,SAAS,MAAMwC,OAAOC;gBAC5B,IAAIzC,QAAQ;oBACRP,QAAQiE,GAAG,CAACjB,UAAUzC;gBAC1B,OAAO;oBACHP,QAAQqE,MAAM,CAACrB;gBACnB;YACJ,EAAE,OAAOsB,KAAU;gBACfC,QAAQC,KAAK,CAACF,IAAIG,OAAO;gBACzBzE,QAAQiE,GAAG,CAACjB,UAAUsB;YAC1B;QACJ;QAEA,IAAI1E,WAAW8E,KAAK,EAAE;YAClB,MAAMC,UAAU,MAAMC,IAAAA,qBAAY,EAC9BhF,WAAW+D,SAAS,EACpB/D,WAAWkE,eAAe;YAE9Ba,QAAQE,EAAE,CAAC,SAAS;gBAChBC,QAAQC,OAAO,GACVC,IAAI,CAAC;oBACF1C,MAAK2C,uBAAuB,CAACjF,SAASJ,WAAWsF,KAAK;oBACtD,MAAMjD,OAAOjC,QAAQmF,MAAM;oBAC3B,IAAI,CAACvF,WAAWsF,KAAK,EAAE;wBACnBX,QAAQa,IAAI,CAAC;oBACjB;gBACJ,GACCC,KAAK,CAACf,CAAAA;oBACHC,QAAQC,KAAK,CAACF,IAAIG,OAAO;gBAC7B;YACR;YACAE,QAAQE,EAAE,CAAC,OAAO,OAAM7B;gBACpB,IAAIe,IAAAA,8BAAqB,EAACf,UAAUpD,WAAWoE,UAAU,GAAG;oBACxD,yDAAyD;oBACzDhE,UAAU,MAAMuD,WAAWvD;gBAC/B;YACJ;YACA2E,QAAQE,EAAE,CAAC,UAAU7B,CAAAA;gBACjBhD,QAAQqE,MAAM,CAACrB;YACnB;YACA,KAAK,MAAMsC,QAAQ;gBAAC;gBAAO;aAAS,CAAE;gBAClCX,QAAQE,EAAE,CAACS,MAAMtC,CAAAA;oBACb,IACI,CAACe,IAAAA,8BAAqB,EAACf,UAAUpD,WAAWoE,UAAU,GACxD;wBACE;oBACJ;oBAEA,MAAMuB,QAAQ9C,QAAQ+C,MAAM;oBAE5BzC,OAAOC,UACFgC,IAAI,CAAC,OAAMzE;wBACR,IAAI,CAACA,QAAQ;4BACTP,QAAQqE,MAAM,CAACrB;4BACf;wBACJ;wBACAhD,QAAQiE,GAAG,CAACjB,UAAUzC;wBACtB+B,MAAK2C,uBAAuB,CAACjF,SAAS;wBACtC,MAAMiC,OAAOjC,QAAQmF,MAAM;wBAC3B,IAAI,CAACvF,WAAWsF,KAAK,EAAE;4BACnB,MAAM,CAACO,SAASC,YAAY,GACxBjD,QAAQ+C,MAAM,CAACD;4BACnB,MAAMI,KAAKF,UAAU,OAAOC,cAAc;4BAC1C,MAAME,OAAOzD,aAAI,CAACC,QAAQ,CAACxC,WAAWyC,OAAO;4BAC7CkC,QAAQsB,GAAG,CACP,CAAC,SAAS,EAAED,KAAK,IAAI,EAAED,GAAGG,OAAO,CAAC,GAAG,EAAE,CAAC;wBAEhD;oBACJ,GACCT,KAAK,CAACf,CAAAA;wBACHC,QAAQC,KAAK,CAACF,IAAIG,OAAO;oBAC7B;gBACR;YACJ;QACJ,OAAO;YACHnC,MAAK2C,uBAAuB,CAACjF,SAASJ,WAAWsF,KAAK;YACtD,MAAMjD,OAAOjC,QAAQmF,MAAM;QAC/B;IACJ;IAEA,eAAeY;QACX,IAAI1F,OAAO;QACXoC,QAAQsD,KAAK,CAACC,WAAW,CAAC;QAC1B,WAAW,MAAMC,SAASxD,QAAQsD,KAAK,CAAE;YACrC1F,QAAQ4F;QACZ;QACA,MAAMC,MAAM,MAAM5D,MAAK6D,SAAS,CAC5BvG,WAAWoD,QAAQ,EACnB3C,MACA;YACI,GAAGR,UAAU;YACboD,gBAAgB;QACpB,GACArD,WAAW0D,IAAI,EACf8C;QAGJnE,OAAO;YAACiE;SAAI;IAChB;IAEA,IAAItG,WAAW+D,SAAS,CAAC7B,MAAM,EAAE;QAC7B,MAAMqC;IACV,OAAO;QACH,MAAM4B;IACV;AACJ"}