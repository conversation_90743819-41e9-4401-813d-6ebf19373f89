{"version": 3, "sources": ["../../src/swc/sources.ts"], "sourcesContent": ["import glob from \"fast-glob\";\nimport slash from \"slash\";\nimport { stat } from \"fs\";\nimport { join, basename, extname } from \"path\";\nimport { minimatch } from \"minimatch\";\n\n/**\n * Find all input files based on source globs\n */\nexport async function globSources(\n    sources: string[],\n    only: string[],\n    ignore: string[],\n    includeDotfiles = false\n): Promise<string[]> {\n    const globConfig: glob.Options = {\n        dot: includeDotfiles,\n        ignore,\n    };\n\n    const files = await Promise.all(\n        sources\n            .filter(\n                source =>\n                    includeDotfiles ||\n                    source === \".\" ||\n                    !basename(source).startsWith(\".\")\n            )\n            .map(source => {\n                return new Promise<string[]>(resolve => {\n                    stat(source, (err, stat) => {\n                        if (err) {\n                            resolve([]);\n                            return;\n                        }\n                        if (!stat.isDirectory()) {\n                            resolve([source]);\n                        } else {\n                            glob(slash(join(source, \"**\")), globConfig)\n                                .then(matches => resolve(matches))\n                                .catch(() => resolve([]));\n                        }\n                    });\n                });\n            })\n    );\n\n    const f = files.flat().filter(filename => {\n        return (\n            !only ||\n            only.length === 0 ||\n            only.some(only => minimatch(slash(filename), only))\n        );\n    });\n\n    return Array.from(new Set<string>(f));\n}\n\ntype Split = [compilable: string[], copyable: string[]];\n\n/**\n * Test if a filename ends with a compilable extension.\n */\nexport function isCompilableExtension(\n    filename: string,\n    allowedExtension: string[]\n): boolean {\n    const ext = extname(filename);\n    return allowedExtension.includes(ext);\n}\n\n/**\n * Split file list to files that can be compiled and copied\n */\nexport function splitCompilableAndCopyable(\n    files: string[],\n    allowedExtension: string[],\n    copyFiles: boolean\n): Split {\n    const compilable: string[] = [];\n    const copyable: string[] = [];\n\n    for (const file of files) {\n        const isCompilable = isCompilableExtension(file, allowedExtension);\n\n        if (isCompilable) {\n            compilable.push(file);\n        } else if (copyFiles) {\n            copyable.push(file);\n        }\n    }\n\n    return [compilable, copyable];\n}\n\nexport async function requireChokidar() {\n    try {\n        const { default: chokidar } = await import(\"chokidar\");\n        return chokidar;\n    } catch (err) {\n        console.error(\n            \"The optional dependency chokidar is not installed and is required for \" +\n                \"--watch. Chokidar is likely not supported on your platform.\"\n        );\n        throw err;\n    }\n}\n\nexport async function watchSources(sources: string[], includeDotfiles = false) {\n    const chokidar = await requireChokidar();\n\n    return chokidar.watch(sources, {\n        ignored: includeDotfiles\n            ? undefined\n            : (filename: string) => basename(filename).startsWith(\".\"),\n        ignoreInitial: true,\n        awaitWriteFinish: {\n            stabilityThreshold: 50,\n            pollInterval: 10,\n        },\n    });\n}\n"], "names": ["globSources", "isCompilableExtension", "requireChokidar", "splitCompilableAndCopyable", "watchSources", "sources", "only", "ignore", "includeDotfiles", "globConfig", "dot", "files", "Promise", "all", "filter", "source", "basename", "startsWith", "map", "resolve", "stat", "err", "isDirectory", "glob", "slash", "join", "then", "matches", "catch", "f", "flat", "filename", "length", "some", "minimatch", "Array", "from", "Set", "allowedExtension", "ext", "extname", "includes", "copyFiles", "compilable", "copyable", "file", "isCompilable", "push", "default", "chokidar", "console", "error", "watch", "ignored", "undefined", "ignoreInitial", "awaitWriteFinish", "stabilityThreshold", "pollInterval"], "mappings": ";;;;;;;;;;;IASsBA,WAAW;eAAXA;;IAsDNC,qBAAqB;eAArBA;;IAgCMC,eAAe;eAAfA;;IArBNC,0BAA0B;eAA1BA;;IAkCMC,YAAY;eAAZA;;;iEA5GL;8DACC;oBACG;sBACmB;2BACd;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKnB,eAAeJ,YAClBK,OAAiB,EACjBC,IAAc,EACdC,MAAgB,EAChBC,kBAAkB,KAAK;IAEvB,MAAMC,aAA2B;QAC7BC,KAAKF;QACLD;IACJ;IAEA,MAAMI,QAAQ,MAAMC,QAAQC,GAAG,CAC3BR,QACKS,MAAM,CACHC,CAAAA,SACIP,mBACAO,WAAW,OACX,CAACC,IAAAA,cAAQ,EAACD,QAAQE,UAAU,CAAC,MAEpCC,GAAG,CAACH,CAAAA;QACD,OAAO,IAAIH,QAAkBO,CAAAA;YACzBC,IAAAA,QAAI,EAACL,QAAQ,CAACM,KAAKD;gBACf,IAAIC,KAAK;oBACLF,QAAQ,EAAE;oBACV;gBACJ;gBACA,IAAI,CAACC,KAAKE,WAAW,IAAI;oBACrBH,QAAQ;wBAACJ;qBAAO;gBACpB,OAAO;oBACHQ,IAAAA,iBAAI,EAACC,IAAAA,cAAK,EAACC,IAAAA,UAAI,EAACV,QAAQ,QAAQN,YAC3BiB,IAAI,CAACC,CAAAA,UAAWR,QAAQQ,UACxBC,KAAK,CAAC,IAAMT,QAAQ,EAAE;gBAC/B;YACJ;QACJ;IACJ;IAGR,MAAMU,IAAIlB,MAAMmB,IAAI,GAAGhB,MAAM,CAACiB,CAAAA;QAC1B,OACI,CAACzB,QACDA,KAAK0B,MAAM,KAAK,KAChB1B,KAAK2B,IAAI,CAAC3B,CAAAA,OAAQ4B,IAAAA,oBAAS,EAACV,IAAAA,cAAK,EAACO,WAAWzB;IAErD;IAEA,OAAO6B,MAAMC,IAAI,CAAC,IAAIC,IAAYR;AACtC;AAOO,SAAS5B,sBACZ8B,QAAgB,EAChBO,gBAA0B;IAE1B,MAAMC,MAAMC,IAAAA,aAAO,EAACT;IACpB,OAAOO,iBAAiBG,QAAQ,CAACF;AACrC;AAKO,SAASpC,2BACZQ,KAAe,EACf2B,gBAA0B,EAC1BI,SAAkB;IAElB,MAAMC,aAAuB,EAAE;IAC/B,MAAMC,WAAqB,EAAE;IAE7B,KAAK,MAAMC,QAAQlC,MAAO;QACtB,MAAMmC,eAAe7C,sBAAsB4C,MAAMP;QAEjD,IAAIQ,cAAc;YACdH,WAAWI,IAAI,CAACF;QACpB,OAAO,IAAIH,WAAW;YAClBE,SAASG,IAAI,CAACF;QAClB;IACJ;IAEA,OAAO;QAACF;QAAYC;KAAS;AACjC;AAEO,eAAe1C;IAClB,IAAI;QACA,MAAM,EAAE8C,SAASC,QAAQ,EAAE,GAAG,MAAM,mEAAA,QAAO;QAC3C,OAAOA;IACX,EAAE,OAAO5B,KAAK;QACV6B,QAAQC,KAAK,CACT,2EACI;QAER,MAAM9B;IACV;AACJ;AAEO,eAAejB,aAAaC,OAAiB,EAAEG,kBAAkB,KAAK;IACzE,MAAMyC,WAAW,MAAM/C;IAEvB,OAAO+C,SAASG,KAAK,CAAC/C,SAAS;QAC3BgD,SAAS7C,kBACH8C,YACA,CAACvB,WAAqBf,IAAAA,cAAQ,EAACe,UAAUd,UAAU,CAAC;QAC1DsC,eAAe;QACfC,kBAAkB;YACdC,oBAAoB;YACpBC,cAAc;QAClB;IACJ;AACJ"}